import React, { useState, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Search, MessageCircle, Paperclip } from 'lucide-react';
import { WhatsAppChat } from '@/components/WhatsAppChat';
import { CustomerAIChat } from '@/components/CustomerAIChat';

const AI_BOTS = [
  {
    id: 'customer',
    name: 'AI Pelanggan',
    description: 'Kelola invoice dan pembayaran pelanggan',
    avatar: 'AP',
    lastMessage: 'Siap membantu transaksi pelanggan',
    time: 'sekarang',
    color: 'bg-blue-500'
  },
  {
    id: 'vendor',
    name: 'AI Vendor',
    description: 'Kelola tagihan dan pengeluaran vendor',
    avatar: 'AV',
    lastMessage: 'Pantau pembayaran vendor di sini',
    time: 'sekarang',
    color: 'bg-green-500'
  },
  {
    id: 'general',
    name: 'AI Transaksi Umum',
    description: 'Semua transaksi keuangan lainnya',
    avatar: 'TU',
    lastMessage: 'Catat transaksi keuangan apapun',
    time: 'sekarang',
    color: 'bg-purple-500'
  },
  {
    id: 'pettycash',
    name: 'AI Kas Kecil',
    description: 'Transaksi kas kecil dan persetujuan',
    avatar: 'KK',
    lastMessage: 'Ajukan permintaan kas kecil',
    time: 'sekarang',
    color: 'bg-orange-500'
  }
];

export const ChatTab = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File[]}>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const filteredBots = AI_BOTS.filter(bot =>
    bot.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, botId: string) => {
    const files = event.target.files;
    if (files) {
      const fileArray = Array.from(files);
      setUploadedFiles(prev => ({
        ...prev,
        [botId]: [...(prev[botId] || []), ...fileArray]
      }));
    }
  };

  const removeFile = (botId: string, fileIndex: number) => {
    setUploadedFiles(prev => ({
      ...prev,
      [botId]: prev[botId]?.filter((_, index) => index !== fileIndex) || []
    }));
  };

  // Show CustomerAIChat for Customer AI
  if (selectedChat === 'customer') {
    return <CustomerAIChat onBack={() => setSelectedChat(null)} />;
  }

  // Show WhatsApp chat for General Transaction
  if (selectedChat === 'general') {
    return <WhatsAppChat onBack={() => setSelectedChat(null)} />;
  }

  if (selectedChat) {
    const selectedBot = AI_BOTS.find(bot => bot.id === selectedChat);
    const chatFiles = uploadedFiles[selectedChat] || [];

    return (
      <div className="flex flex-col h-[calc(100vh-60px)]">
        {/* Chat Header */}
        <div className="flex items-center gap-3 p-4 border-b bg-background">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setSelectedChat(null)}
          >
            ←
          </Button>
          <Avatar className="w-10 h-10">
            <AvatarFallback className={selectedBot?.color}>
              {selectedBot?.avatar}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="font-semibold">{selectedBot?.name}</h3>
            <p className="text-sm text-muted-foreground">Online</p>
          </div>
        </div>

        {/* Uploaded Files Display */}
        {chatFiles.length > 0 && (
          <div className="p-4 border-b bg-muted/50">
            <h4 className="text-sm font-medium mb-2">File yang diunggah:</h4>
            <div className="flex flex-wrap gap-2">
              {chatFiles.map((file, index) => (
                <div key={index} className="flex items-center gap-2 bg-background px-3 py-1 rounded-md text-sm">
                  <Paperclip className="w-3 h-3" />
                  <span className="truncate max-w-32">{file.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive/20"
                    onClick={() => removeFile(selectedChat, index)}
                  >
                    ×
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Chat Messages */}
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          <div className="bg-muted p-3 rounded-lg max-w-xs">
            <p className="text-sm">Halo! Saya {selectedBot?.name}. {selectedBot?.description}</p>
            <span className="text-xs text-muted-foreground">baru saja</span>
          </div>
        </div>

        {/* Message Input */}
        <div className="p-4 border-t bg-background">
          <div className="flex gap-2">
            <Input placeholder="Ketik pesan..." className="flex-1" />
            <Button 
              variant="outline" 
              size="icon"
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="w-4 h-4" />
            </Button>
            <Button>Kirim</Button>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={(e) => handleFileUpload(e, selectedChat)}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="p-4 border-b">
        <h1 className="text-2xl font-bold mb-4">Chat</h1>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Cari atau mulai chat baru"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* AI Bots List */}
      <div className="overflow-y-auto">
        {filteredBots.map((bot) => (
          <Card 
            key={bot.id} 
            className="m-2 p-4 cursor-pointer hover:bg-accent transition-colors"
            onClick={() => setSelectedChat(bot.id)}
          >
            <div className="flex items-center gap-3">
              <Avatar className="w-12 h-12">
                <AvatarFallback className={bot.color}>
                  {bot.avatar}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-semibold truncate">{bot.name}</h3>
                  <span className="text-xs text-muted-foreground">{bot.time}</span>
                </div>
                <p className="text-sm text-muted-foreground truncate">
                  {bot.id === 'general' ? 'Lihat chat WhatsApp Anda di sini' : bot.lastMessage}
                </p>
              </div>
              <MessageCircle className="w-4 h-4 text-muted-foreground" />
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
