import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Building2, Users, CreditCard, QrCode } from 'lucide-react';
import { toast } from 'sonner';

interface PaywallModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentPlan?: 'free' | 'premium' | 'enterprise';
  onUpgrade?: (plan: 'premium' | 'enterprise') => void;
}

interface PlanFeature {
  name: string;
  included: boolean;
}

interface Plan {
  id: 'free' | 'premium' | 'enterprise';
  name: string;
  price: {
    monthly: number;
    yearly: number;
  };
  maxUsers: number | 'unlimited';
  icon: React.ReactNode;
  popular?: boolean;
  features: PlanFeature[];
}

export const PaywallModal: React.FC<PaywallModalProps> = ({
  open,
  onOpenChange,
  currentPlan = 'free',
  onUpgrade
}) => {
  const [selectedPlan, setSelectedPlan] = useState<'premium' | 'enterprise' | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [showPayment, setShowPayment] = useState(false);

  const plans: Plan[] = [
    {
      id: 'free',
      name: 'Free Plan',
      price: { monthly: 0, yearly: 0 },
      maxUsers: 1,
      icon: <Users className="w-6 h-6" />,
      features: [
        { name: 'Basic Transaction Management', included: true },
        { name: 'Simple Reports', included: true },
        { name: 'Single User Access', included: true },
        { name: 'Team Management', included: false },
        { name: 'Petty Cash Management', included: false },
        { name: 'Advanced Reports', included: false },
        { name: 'Role-based Access', included: false },
        { name: 'API Access', included: false },
      ]
    },
    {
      id: 'premium',
      name: 'Premium Plan',
      price: { monthly: 99000, yearly: 990000 },
      maxUsers: 3,
      icon: <Crown className="w-6 h-6" />,
      popular: true,
      features: [
        { name: 'All Free Features', included: true },
        { name: 'Team Management (up to 3 users)', included: true },
        { name: 'Petty Cash Management', included: true },
        { name: 'Role-based Access Control', included: true },
        { name: 'Advanced Reports & Analytics', included: true },
        { name: 'Purchase & Sales Management', included: true },
        { name: 'Priority Support', included: true },
        { name: 'API Access', included: false },
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise Plan',
      price: { monthly: 299000, yearly: 2990000 },
      maxUsers: 'unlimited',
      icon: <Building2 className="w-6 h-6" />,
      features: [
        { name: 'All Premium Features', included: true },
        { name: 'Unlimited Team Members', included: true },
        { name: 'Advanced Petty Cash System', included: true },
        { name: 'Custom Role Management', included: true },
        { name: 'White-label Solution', included: true },
        { name: 'API Access & Integrations', included: true },
        { name: 'Dedicated Account Manager', included: true },
        { name: 'Custom Training & Onboarding', included: true },
      ]
    }
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handleSelectPlan = (planId: 'premium' | 'enterprise') => {
    setSelectedPlan(planId);
    setShowPayment(true);
  };

  const handlePayment = () => {
    // Simulate payment process
    toast.success(`Berhasil upgrade ke ${selectedPlan === 'premium' ? 'Premium' : 'Enterprise'} Plan!`);
    onUpgrade?.(selectedPlan!);
    onOpenChange(false);
    setShowPayment(false);
    setSelectedPlan(null);
  };

  if (showPayment && selectedPlan) {
    const plan = plans.find(p => p.id === selectedPlan)!;
    const price = billingCycle === 'monthly' ? plan.price.monthly : plan.price.yearly;
    
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Pembayaran {plan.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Ringkasan Pesanan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>{plan.name}</span>
                  <span className="font-semibold">{formatPrice(price)}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Periode</span>
                  <span>{billingCycle === 'monthly' ? 'Bulanan' : 'Tahunan'}</span>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{formatPrice(price)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods */}
            <div className="space-y-4">
              <h3 className="font-semibold">Metode Pembayaran</h3>
              
              {/* QR Code Payment */}
              <Card className="border-2 border-blue-200">
                <CardContent className="p-4">
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2">
                      <QrCode className="w-5 h-5" />
                      <span className="font-semibold">QRIS / E-Wallet</span>
                    </div>
                    
                    {/* Dummy QR Code */}
                    <div className="bg-white p-4 rounded-lg border inline-block">
                      <div className="w-48 h-48 bg-gray-100 flex items-center justify-center rounded">
                        <div className="text-center">
                          <QrCode className="w-16 h-16 mx-auto mb-2 text-gray-400" />
                          <p className="text-sm text-gray-500">Dummy QR Code</p>
                          <p className="text-xs text-gray-400">Scan untuk pembayaran</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-sm text-muted-foreground">
                      <p>Scan QR code dengan aplikasi:</p>
                      <p className="font-medium">GoPay • OVO • DANA • ShopeePay</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Bank Transfer */}
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CreditCard className="w-5 h-5" />
                      <span className="font-semibold">Transfer Bank</span>
                    </div>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      <p><strong>Bank BCA</strong></p>
                      <p>No. Rekening: **********</p>
                      <p>A.n: PT Finance App Indonesia</p>
                      <p className="text-red-600 font-medium">Total: {formatPrice(price)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button 
                variant="outline" 
                onClick={() => setShowPayment(false)}
                className="flex-1"
              >
                Kembali
              </Button>
              <Button 
                onClick={handlePayment}
                className="flex-1"
              >
                Konfirmasi Pembayaran
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl">
            Pilih Paket Berlangganan
          </DialogTitle>
          <p className="text-center text-muted-foreground">
            Upgrade untuk mengakses fitur team management dan petty cash
          </p>
        </DialogHeader>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-6">
          <div className="bg-muted p-1 rounded-lg">
            <Button
              variant={billingCycle === 'monthly' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setBillingCycle('monthly')}
            >
              Bulanan
            </Button>
            <Button
              variant={billingCycle === 'yearly' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setBillingCycle('yearly')}
            >
              Tahunan
              <Badge variant="secondary" className="ml-2">Save 17%</Badge>
            </Button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card 
              key={plan.id}
              className={`relative ${
                plan.popular ? 'border-blue-500 shadow-lg' : ''
              } ${
                currentPlan === plan.id ? 'bg-green-50 border-green-500' : ''
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                  Most Popular
                </Badge>
              )}
              
              {currentPlan === plan.id && (
                <Badge className="absolute -top-3 right-4 bg-green-500">
                  Current Plan
                </Badge>
              )}

              <CardHeader className="text-center">
                <div className="flex justify-center mb-2">
                  {plan.icon}
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="space-y-1">
                  <div className="text-3xl font-bold">
                    {plan.price[billingCycle] === 0 ? (
                      'Gratis'
                    ) : (
                      formatPrice(plan.price[billingCycle])
                    )}
                  </div>
                  {plan.price[billingCycle] > 0 && (
                    <div className="text-sm text-muted-foreground">
                      per {billingCycle === 'monthly' ? 'bulan' : 'tahun'}
                    </div>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {typeof plan.maxUsers === 'number' 
                    ? `Maksimal ${plan.maxUsers} user${plan.maxUsers > 1 ? 's' : ''}`
                    : 'Unlimited users'
                  }
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check 
                        className={`w-4 h-4 mt-0.5 ${
                          feature.included 
                            ? 'text-green-500' 
                            : 'text-gray-300'
                        }`} 
                      />
                      <span className={`text-sm ${
                        feature.included 
                          ? 'text-foreground' 
                          : 'text-muted-foreground line-through'
                      }`}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>

                <Button
                  className="w-full"
                  variant={currentPlan === plan.id ? 'outline' : 'default'}
                  disabled={currentPlan === plan.id || plan.id === 'free'}
                  onClick={() => plan.id !== 'free' && handleSelectPlan(plan.id)}
                >
                  {currentPlan === plan.id 
                    ? 'Paket Aktif' 
                    : plan.id === 'free' 
                      ? 'Paket Gratis' 
                      : 'Pilih Paket'
                  }
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};
