import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for vendor bill from API
interface VendorBill {
  id: number;
  bill_number: string | null;
  vendor: {
    display_name: string;
  };
  due_date: string;
  formatted_due_date: string;
  total: number;
  total_formatted: string;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
}

interface DueDateVendorDialogProps {
  vendorBills: VendorBill[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const DueDateVendorDialog: React.FC<DueDateVendorDialogProps> = ({
  vendorBills,
  open,
  onOpenChange,
}) => {
  const [countdown, setCountdown] = useState<string>('');

  // Get nearest due date
  const today = new Date();
  const unpaidBills = vendorBills.filter(b => !b.is_fully_paid);
  
  const nearestBill = unpaidBills
    .map(bill => ({
      ...bill,
      dueDate: new Date(bill.due_date),
      daysUntilDue: Math.ceil((new Date(bill.due_date).getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    }))
    .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())[0];

  // Update countdown every second
  useEffect(() => {
    if (!nearestBill) return;

    const updateCountdown = () => {
      const now = new Date();
      const dueDate = nearestBill.dueDate;
      const timeDiff = dueDate.getTime() - now.getTime();

      if (timeDiff <= 0) {
        setCountdown('OVERDUE');
        return;
      }

      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      if (days > 0) {
        setCountdown(`${days}d ${hours}h ${minutes}m ${seconds}s`);
      } else if (hours > 0) {
        setCountdown(`${hours}h ${minutes}m ${seconds}s`);
      } else {
        setCountdown(`${minutes}m ${seconds}s`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [nearestBill]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (bill: any) => {
    if (bill.is_overdue) return 'bg-red-100 text-red-800';
    if (bill.daysUntilDue <= 3) return 'bg-orange-100 text-orange-800';
    if (bill.daysUntilDue <= 7) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = (bill: any) => {
    if (bill.is_overdue) return `Overdue ${Math.abs(bill.daysUntilDue)} days`;
    if (bill.daysUntilDue <= 0) return 'Due Today';
    return `${bill.daysUntilDue} days left`;
  };

  // Sort bills by due date
  const sortedBills = unpaidBills
    .map(bill => ({
      ...bill,
      dueDate: new Date(bill.due_date),
      daysUntilDue: Math.ceil((new Date(bill.due_date).getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    }))
    .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Due Date Vendor</span>
            <Badge className="bg-red-100 text-red-800">
              {unpaidBills.length} Tagihan Belum Lunas
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Nearest Due Date Card */}
          {nearestBill && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-lg text-red-800">Tagihan Terdekat</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-muted-foreground">Bill Number:</span>
                    <p className="font-semibold">{nearestBill.bill_number || `#${nearestBill.id}`}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Vendor:</span>
                    <p className="font-semibold">{nearestBill.vendor.display_name}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Due Date:</span>
                    <p className="font-semibold">{formatDate(nearestBill.due_date)}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Amount:</span>
                    <p className="font-semibold text-lg">{formatCurrency(nearestBill.total)}</p>
                  </div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg border">
                  <div className="text-sm text-muted-foreground mb-2">Countdown</div>
                  <div className={`text-2xl font-bold ${countdown === 'OVERDUE' ? 'text-red-600' : 'text-red-600'}`}>
                    {countdown}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* All Bills Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Semua Tagihan Vendor Belum Lunas</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Bill Number</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedBills.map((bill) => (
                    <TableRow key={bill.id}>
                      <TableCell className="font-medium">
                        {bill.bill_number || `#${bill.id}`}
                      </TableCell>
                      <TableCell>
                        {bill.vendor.display_name}
                      </TableCell>
                      <TableCell>
                        {formatDate(bill.due_date)}
                      </TableCell>
                      <TableCell className="font-semibold">
                        {formatCurrency(bill.total)}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(bill)}>
                          {getStatusText(bill)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {sortedBills.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  Tidak ada tagihan vendor yang belum lunas
                </div>
              )}
            </CardContent>
          </Card>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Ringkasan</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Total Tagihan:</span>
                <p className="text-2xl font-bold text-blue-600">{unpaidBills.length}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Total Amount:</span>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(unpaidBills.reduce((sum, bill) => sum + bill.total, 0))}
                </p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Overdue:</span>
                <p className="text-2xl font-bold text-red-600">
                  {sortedBills.filter(bill => bill.daysUntilDue < 0).length}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
