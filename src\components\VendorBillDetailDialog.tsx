
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for vendor bills from BigCapital API
interface VendorBill {
  id: number;
  bill_number: string | null;
  vendor_id: number;
  vendor: {
    id: number;
    contact_service: string;
    contact_type: string | null;
    balance: number;
    currency_code: string;
    first_name: string;
    last_name: string | null;
    company_name: string | null;
    display_name: string;
    email: string | null;
    work_phone: string | null;
    personal_phone: string | null;
    created_at: string;
    updated_at: string;
  };
  bill_date: string;
  formatted_bill_date: string;
  due_date: string;
  formatted_due_date: string;
  total: number;
  total_formatted: string;
  balance: number;
  formatted_balance: string;
  due_amount: number;
  formatted_due_amount: string;
  payment_amount: number;
  formatted_payment_amount: string;
  currency_code: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  note: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string | null;
    };
  }>;
}

interface VendorBillDetailDialogProps {
  vendorBill: VendorBill | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const VendorBillDetailDialog: React.FC<VendorBillDetailDialogProps> = ({
  vendorBill,
  open,
  onOpenChange,
}) => {
  if (!vendorBill) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const getStatusColor = () => {
    if (vendorBill.is_fully_paid) return 'bg-green-100 text-green-800';
    if (vendorBill.is_overdue) return 'bg-red-100 text-red-800';
    if (vendorBill.is_partially_paid) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getStatusText = () => {
    if (vendorBill.is_fully_paid) return 'Lunas';
    if (vendorBill.is_overdue) return 'Terlambat';
    if (vendorBill.is_partially_paid) return 'Sebagian Terbayar';
    return 'Belum Bayar';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Detail Tagihan {vendorBill.bill_number || `#${vendorBill.id}`}</span>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Bill Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Tagihan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Nomor Tagihan:</span>
                  <p className="font-semibold">{vendorBill.bill_number || `#${vendorBill.id}`}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Vendor:</span>
                  <p className="font-semibold">{vendorBill.vendor.display_name}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Tanggal Tagihan:</span>
                  <p>{vendorBill.formatted_bill_date}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Jatuh Tempo:</span>
                  <p>{vendorBill.formatted_due_date}</p>
                </div>
                {vendorBill.reference_no && (
                  <div>
                    <span className="text-sm text-muted-foreground">Nomor Referensi:</span>
                    <p>{vendorBill.reference_no}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ringkasan Pembayaran</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Total Tagihan:</span>
                  <p className="text-2xl font-bold text-red-600">
                    {vendorBill.total_formatted}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Jumlah Terbayar:</span>
                  <p className="font-semibold">{vendorBill.formatted_payment_amount}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Sisa Tagihan:</span>
                  <p className="font-semibold">{vendorBill.formatted_due_amount}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <p className="font-semibold">{getStatusText()}</p>
                </div>
                {vendorBill.is_overdue && (
                  <div>
                    <span className="text-sm text-muted-foreground">Terlambat:</span>
                    <p className="font-semibold text-red-600">{vendorBill.overdue_days} hari</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Vendor Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Vendor</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Nama:</span>
                <p className="font-semibold">{vendorBill.vendor.display_name}</p>
              </div>
              {vendorBill.vendor.company_name && (
                <div>
                  <span className="text-sm text-muted-foreground">Perusahaan:</span>
                  <p>{vendorBill.vendor.company_name}</p>
                </div>
              )}
              {vendorBill.vendor.email && (
                <div>
                  <span className="text-sm text-muted-foreground">Email:</span>
                  <p>{vendorBill.vendor.email}</p>
                </div>
              )}
              {vendorBill.vendor.work_phone && (
                <div>
                  <span className="text-sm text-muted-foreground">Telepon:</span>
                  <p>{vendorBill.vendor.work_phone}</p>
                </div>
              )}
              <div>
                <span className="text-sm text-muted-foreground">Saldo Vendor:</span>
                <p className="font-semibold">{formatCurrency(vendorBill.vendor.balance)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Bill Items */}
          {vendorBill.entries && vendorBill.entries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Item Tagihan</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Harga</TableHead>
                      <TableHead>Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {vendorBill.entries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">
                          {entry.item.name}
                        </TableCell>
                        <TableCell>
                          {entry.description || '-'}
                        </TableCell>
                        <TableCell>
                          {entry.quantity}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(entry.rate)}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(entry.total)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Additional Information */}
          {vendorBill.note && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{vendorBill.note}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
