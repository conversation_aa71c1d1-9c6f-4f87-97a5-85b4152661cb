import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for cash transactions
interface CashTransaction {
  id: string;
  type: string;
  description: string;
  amount: number;
  date: string;
  time: string;
  status: string;
  journal?: any;
}

interface CashFlowDialogProps {
  cashTransactions: CashTransaction[];
  cashInflow: number;
  cashOutflow: number;
  netCash: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CashFlowDialog: React.FC<CashFlowDialogProps> = ({
  cashTransactions,
  cashInflow,
  cashOutflow,
  netCash,
  open,
  onOpenChange,
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Separate inflow and outflow transactions
  const inflowTransactions = cashTransactions.filter(t => t.amount > 0);
  const outflowTransactions = cashTransactions.filter(t => t.amount < 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Cash Flow Detail</span>
            <Badge className="bg-blue-100 text-blue-800">
              {cashTransactions.length} Transaksi Kas
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Cash Inflow</div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(cashInflow)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {inflowTransactions.length} transaksi
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Cash Outflow</div>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(cashOutflow)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {outflowTransactions.length} transaksi
                </div>
              </CardContent>
            </Card>
            
            <Card className={`border-2 ${netCash >= 0 ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Net Cash</div>
                <div className={`text-2xl font-bold ${netCash >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(netCash)}
                </div>
                <div className="text-xs text-muted-foreground">
                  Kas masuk - kas keluar
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cash Inflow Table */}
          {inflowTransactions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-green-600">Cash Inflow</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tanggal</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Akun Kas</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inflowTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{formatDate(transaction.date)}</p>
                            <p className="text-sm text-muted-foreground">{transaction.time}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-sm text-muted-foreground">{transaction.type}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {transaction.journal && transaction.journal.entries
                            .filter((entry: any) => 
                              entry.account.account_type === 'cash' || 
                              entry.account.account_type === 'bank' ||
                              entry.account.name.toLowerCase().includes('kas') ||
                              entry.account.name.toLowerCase().includes('bank')
                            )
                            .map((entry: any, index: number) => (
                              <div key={index} className="text-sm">
                                <p className="font-medium">{entry.account.name}</p>
                                <p className="text-muted-foreground">{entry.account.account_type_label || entry.account.account_type}</p>
                              </div>
                            ))
                          }
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="font-semibold text-green-600">
                            +{formatCurrency(transaction.amount)}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Cash Outflow Table */}
          {outflowTransactions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-red-600">Cash Outflow</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tanggal</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Akun Kas</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {outflowTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{formatDate(transaction.date)}</p>
                            <p className="text-sm text-muted-foreground">{transaction.time}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-sm text-muted-foreground">{transaction.type}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {transaction.journal && transaction.journal.entries
                            .filter((entry: any) => 
                              entry.account.account_type === 'cash' || 
                              entry.account.account_type === 'bank' ||
                              entry.account.name.toLowerCase().includes('kas') ||
                              entry.account.name.toLowerCase().includes('bank')
                            )
                            .map((entry: any, index: number) => (
                              <div key={index} className="text-sm">
                                <p className="font-medium">{entry.account.name}</p>
                                <p className="text-muted-foreground">{entry.account.account_type_label || entry.account.account_type}</p>
                              </div>
                            ))
                          }
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="font-semibold text-red-600">
                            {formatCurrency(transaction.amount)}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* No Cash Transactions */}
          {cashTransactions.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <div className="text-muted-foreground">
                  <p className="text-lg mb-2">Tidak ada transaksi kas</p>
                  <p className="text-sm">Transaksi yang melibatkan akun kas atau bank akan muncul di sini</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Analisis Cash Flow</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Periode Analisis</h4>
                  <p className="text-sm text-muted-foreground">
                    Data berdasarkan transaksi jurnal yang melibatkan akun kas dan bank
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Status Cash Flow</h4>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    netCash > 0 
                      ? 'bg-green-100 text-green-800' 
                      : netCash < 0 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-gray-100 text-gray-800'
                  }`}>
                    {netCash > 0 ? '✓ Positif' : netCash < 0 ? '⚠ Negatif' : '➖ Seimbang'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
