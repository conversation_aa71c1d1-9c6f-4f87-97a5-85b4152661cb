// Test utilities for Supabase connection and functionality
import { supabase, supabaseHelpers } from '@/lib/supabase';

export const testSupabaseConnection = async () => {
  console.log('🔍 Testing Supabase Connection...');
  
  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const connectionTest = await supabaseHelpers.testConnection();
    console.log('Connection test result:', connectionTest);

    // Test 2: Check authentication
    console.log('2. Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('Current user:', user);
    console.log('Auth error:', authError);

    // Test 3: Test report settings table access
    console.log('3. Testing report_settings table access...');
    const { data: tableData, error: tableError } = await supabase
      .from('report_settings')
      .select('*')
      .limit(1);
    
    console.log('Table access result:', { data: tableData, error: tableError });

    // Test 4: Test RPC functions (if user is authenticated)
    if (user) {
      console.log('4. Testing RPC functions...');
      
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_user_report_settings');
        console.log('RPC function test:', { data: rpcData, error: rpcError });
      } catch (rpcErr) {
        console.log('RPC function error:', rpcErr);
      }
    } else {
      console.log('4. Skipping RPC tests - user not authenticated');
    }

    return {
      success: true,
      connection: connectionTest,
      user,
      tableAccess: { data: tableData, error: tableError }
    };

  } catch (error) {
    console.error('❌ Supabase connection test failed:', error);
    return {
      success: false,
      error
    };
  }
};

export const testReportSettingsSave = async (testData = {
  reportType: 'balance-sheet',
  fromDate: '2025-06-01',
  toDate: '2025-06-30',
  companyName: 'Test Company'
}) => {
  console.log('💾 Testing Report Settings Save...');
  
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.log('❌ User not authenticated - cannot test save');
      return { success: false, error: 'User not authenticated' };
    }

    // Test save using RPC function
    const { data, error } = await supabase.rpc('save_report_settings', {
      p_report_type: testData.reportType,
      p_from_date: testData.fromDate,
      p_to_date: testData.toDate,
      p_company_name: testData.companyName
    });

    console.log('Save test result:', { data, error });

    if (error) {
      return { success: false, error };
    }

    // Test load to verify save worked
    const { data: loadData, error: loadError } = await supabase.rpc('get_user_report_settings', {
      p_report_type: testData.reportType
    });

    console.log('Load verification:', { data: loadData, error: loadError });

    return {
      success: true,
      savedData: data,
      loadedData: loadData
    };

  } catch (error) {
    console.error('❌ Report settings save test failed:', error);
    return { success: false, error };
  }
};

export const runAllTests = async () => {
  console.log('🚀 Running all Supabase tests...');
  console.log('=====================================');
  
  const connectionResult = await testSupabaseConnection();
  console.log('=====================================');
  
  if (connectionResult.success && connectionResult.user) {
    const saveResult = await testReportSettingsSave();
    console.log('=====================================');
    
    return {
      connection: connectionResult,
      save: saveResult
    };
  } else {
    console.log('⚠️ Skipping save tests - connection failed or user not authenticated');
    return {
      connection: connectionResult,
      save: { success: false, error: 'Prerequisites not met' }
    };
  }
};

// Export for use in browser console
(window as any).testSupabase = {
  testConnection: testSupabaseConnection,
  testSave: testReportSettingsSave,
  runAll: runAllTests
};
