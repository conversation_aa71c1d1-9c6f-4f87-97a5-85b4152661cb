import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface DemoSwitcherProps {
  currentRole: 'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash' | 'staff';
  currentPlan: 'free' | 'premium' | 'enterprise';
  onRoleChange: (role: 'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash' | 'staff') => void;
  onPlanChange: (plan: 'free' | 'premium' | 'enterprise') => void;
  compact?: boolean;
}

export const DemoSwitcher: React.FC<DemoSwitcherProps> = ({
  currentRole,
  currentPlan,
  onRoleChange,
  onPlanChange,
  compact = false
}) => {
  const demoUsers = [
    { name: 'Administrator', role: 'admin' as const, plan: 'enterprise' as const },
    { name: 'Staff', role: 'staff' as const, plan: 'premium' as const },
    { name: 'Premium User', role: 'petty_cash' as const, plan: 'premium' as const },
    { name: 'Free User', role: 'general' as const, plan: 'free' as const },
    { name: 'Purchase Manager', role: 'purchase' as const, plan: 'premium' as const },
    { name: 'Sales Manager', role: 'sales' as const, plan: 'premium' as const }
  ];

  const handleUserSwitch = (user: typeof demoUsers[0]) => {
    onRoleChange(user.role);
    onPlanChange(user.plan);
    toast.success(`Switched to ${user.name}`);
  };

  if (compact) {
    return (
      <div className="flex flex-wrap gap-1">
        {demoUsers.map((user, index) => (
          <Button
            key={index}
            variant={currentRole === user.role && currentPlan === user.plan ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleUserSwitch(user)}
            className="text-xs"
          >
            {user.name}
          </Button>
        ))}
      </div>
    );
  }

  return (
    <Card className="p-3">
      <div className="text-sm font-medium mb-2">Demo Mode - Switch User:</div>
      <div className="flex flex-wrap gap-2">
        {demoUsers.map((user, index) => (
          <Button
            key={index}
            variant={currentRole === user.role && currentPlan === user.plan ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleUserSwitch(user)}
          >
            {user.name}
          </Button>
        ))}
      </div>
    </Card>
  );
};
