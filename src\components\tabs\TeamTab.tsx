
import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Users, UserPlus, Settings, MessageCircle } from 'lucide-react';

const TEAM_MEMBERS = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: '<PERSON><PERSON><PERSON>',
    permissions: ['setujui_kas_kecil', 'lihat_laporan', 'kelola_tim'],
    avatar: 'BS',
    status: 'aktif'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'Akuntan',
    permissions: ['setujui_kas_kecil', 'lihat_laporan'],
    avatar: 'SW',
    status: 'aktif'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Ketua Tim',
    permissions: ['setujui_kas_kecil'],
    avatar: 'AR',
    status: 'pending'
  }
];

export const TeamTab = () => {
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [showAddMember, setShowAddMember] = useState(false);
  const [whatsappConnected, setWhatsappConnected] = useState(false);

  const handleAddMember = () => {
    console.log('Menambah anggota:', newMemberEmail);
    setNewMemberEmail('');
    setShowAddMember(false);
  };

  const handleConnectWhatsApp = () => {
    console.log('Menghubungkan WhatsApp...');
    setWhatsappConnected(true);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Manajer Keuangan': return 'bg-purple-100 text-purple-800';
      case 'Akuntan': return 'bg-blue-100 text-blue-800';
      case 'Ketua Tim': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'aktif': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'tidak_aktif': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Manajemen Tim</h1>
        
        {/* WhatsApp Integration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Integrasi WhatsApp
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Hubungkan WhatsApp Business</h3>
                <p className="text-sm text-muted-foreground">
                  Aktifkan anggota tim untuk menerima notifikasi dan persetujuan via WhatsApp
                </p>
              </div>
              <div className="flex items-center gap-2">
                {whatsappConnected ? (
                  <Badge className="bg-green-100 text-green-800">Terhubung</Badge>
                ) : (
                  <Button onClick={handleConnectWhatsApp}>
                    Hubungkan WhatsApp
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Team Member */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Tambah Anggota Tim
              </span>
              <Button 
                variant="outline" 
                onClick={() => setShowAddMember(!showAddMember)}
              >
                {showAddMember ? 'Batal' : 'Tambah Anggota'}
              </Button>
            </CardTitle>
          </CardHeader>
          {showAddMember && (
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Alamat Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newMemberEmail}
                    onChange={(e) => setNewMemberEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Izin Default</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">Lihat Laporan</Badge>
                    <Badge variant="outline">Setujui Kas Kecil</Badge>
                  </div>
                </div>
                <Button onClick={handleAddMember} disabled={!newMemberEmail}>
                  Kirim Undangan
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Team Members List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Anggota Tim ({TEAM_MEMBERS.length})</h2>
        
        {TEAM_MEMBERS.map((member) => (
          <Card key={member.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {member.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{member.name}</h3>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getRoleColor(member.role)}>
                        {member.role}
                      </Badge>
                      <Badge className={getStatusColor(member.status)}>
                        {member.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <Button variant="ghost" size="icon">
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              {/* Permissions */}
              <div className="mt-3 pt-3 border-t">
                <div className="text-sm text-muted-foreground mb-2">Izin:</div>
                <div className="flex flex-wrap gap-1">
                  {member.permissions.map((permission) => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {permission.replace('_', ' ')}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
