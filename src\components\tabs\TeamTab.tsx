import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Settings,
  User,
  Bell,
  Shield,
  CreditCard,
  Upload,
  Save,
  Crown
} from 'lucide-react';
import { toast } from 'sonner';
import { PaywallModal } from '@/components/PaywallModal';
import { TeamManagement } from '@/components/TeamManagement';

export const TeamTab = () => {
  // User state - simulate different user plans and roles
  const [userPlan, setUserPlan] = useState<'free' | 'premium' | 'enterprise'>('free');
  const [userRole, setUserRole] = useState<'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash'>('admin');
  const [showPaywall, setShowPaywall] = useState(false);
  
  // Profile state
  const [profile, setProfile] = useState({
    name: 'John Doe',
    email: '<EMAIL>',
    company: 'PT. Example Company',
    phone: '+62 812 3456 7890',
    avatar: ''
  });

  // Settings state
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: false,
      sms: false
    },
    security: {
      twoFactor: false,
      sessionTimeout: 30
    },
    appearance: {
      theme: 'light',
      language: 'id'
    }
  });

  const handleSaveProfile = () => {
    toast.success('Profil berhasil disimpan!');
  };

  const handleSaveSettings = () => {
    toast.success('Pengaturan berhasil disimpan!');
  };

  const handleUpgrade = (plan: 'premium' | 'enterprise') => {
    setUserPlan(plan);
    toast.success(`Berhasil upgrade ke ${plan} plan!`);
  };

  const getPlanInfo = () => {
    const plans = {
      free: {
        name: 'Free Plan',
        maxMembers: 1,
        features: ['Basic features', 'Single user'],
        color: 'bg-gray-500'
      },
      premium: {
        name: 'Premium Plan',
        maxMembers: 3,
        features: ['Team management', 'Petty cash', 'Advanced reports'],
        color: 'bg-yellow-500'
      },
      enterprise: {
        name: 'Enterprise Plan',
        maxMembers: -1,
        features: ['Unlimited users', 'All features', 'API access'],
        color: 'bg-blue-500'
      }
    };
    return plans[userPlan];
  };

  // Demo user switcher for testing different roles
  const demoUsers = [
    { name: 'Admin User', role: 'admin' as const, plan: 'enterprise' as const },
    { name: 'Premium User', role: 'petty_cash' as const, plan: 'premium' as const },
    { name: 'Free User', role: 'general' as const, plan: 'free' as const },
    { name: 'Purchase Manager', role: 'purchase' as const, plan: 'premium' as const },
    { name: 'Sales Manager', role: 'sales' as const, plan: 'premium' as const }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6 max-h-[calc(100vh-120px)] overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="w-8 h-8 text-blue-500" />
          <div>
            <h1 className="text-3xl font-bold">Tim & Settings</h1>
            <p className="text-muted-foreground">
              Kelola anggota tim, profil, dan pengaturan aplikasi
            </p>
          </div>
        </div>

        {/* Demo User Switcher */}
        <Card className="p-3">
          <div className="text-sm font-medium mb-2">Demo Mode - Switch User:</div>
          <div className="flex flex-wrap gap-2">
            {demoUsers.map((user, index) => (
              <Button
                key={index}
                variant={userRole === user.role && userPlan === user.plan ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setUserRole(user.role);
                  setUserPlan(user.plan);
                  setProfile({...profile, name: user.name});
                  toast.success(`Switched to ${user.name}`);
                }}
              >
                {user.name}
              </Button>
            ))}
          </div>
        </Card>
      </div>

      {/* Current Plan Info */}
      <Card className="border-2 border-dashed">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${getPlanInfo().color}`}>
                {userPlan === 'free' ? (
                  <User className="w-5 h-5 text-white" />
                ) : userPlan === 'premium' ? (
                  <Crown className="w-5 h-5 text-white" />
                ) : (
                  <Shield className="w-5 h-5 text-white" />
                )}
              </div>
              <div>
                <h3 className="font-semibold">{getPlanInfo().name}</h3>
                <p className="text-sm text-muted-foreground">
                  Role: {userRole} • {getPlanInfo().features.join(', ')}
                </p>
              </div>
            </div>
            
            {userPlan === 'free' && (
              <Button onClick={() => setShowPaywall(true)}>
                <Crown className="w-4 h-4 mr-2" />
                Upgrade Plan
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Profile Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center gap-6">
            <Avatar className="w-20 h-20">
              <AvatarImage src={profile.avatar} />
              <AvatarFallback className="text-lg">
                {profile.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Upload Photo
              </Button>
              <p className="text-sm text-muted-foreground">
                JPG, PNG up to 2MB
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={profile.name}
                onChange={(e) => setProfile({...profile, name: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={profile.email}
                onChange={(e) => setProfile({...profile, email: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company">Company</Label>
              <Input
                id="company"
                value={profile.company}
                onChange={(e) => setProfile({...profile, company: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={profile.phone}
                onChange={(e) => setProfile({...profile, phone: e.target.value})}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveProfile}>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Team Management Section */}
      <TeamManagement 
        userPlan={userPlan} 
        maxMembers={getPlanInfo().maxMembers}
      />

      {/* Billing Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Subscription & Billing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${getPlanInfo().color}`}>
                {userPlan === 'free' ? (
                  <User className="w-5 h-5 text-white" />
                ) : userPlan === 'premium' ? (
                  <Crown className="w-5 h-5 text-white" />
                ) : (
                  <Shield className="w-5 h-5 text-white" />
                )}
              </div>
              <div>
                <h3 className="font-semibold">{getPlanInfo().name}</h3>
                <p className="text-sm text-muted-foreground">
                  {userPlan === 'free' 
                    ? 'Free forever' 
                    : `Rp ${userPlan === 'premium' ? '99,000' : '299,000'}/month`
                  }
                </p>
              </div>
            </div>
            
            <div className="flex gap-2">
              {userPlan !== 'enterprise' && (
                <Button onClick={() => setShowPaywall(true)}>
                  Upgrade
                </Button>
              )}
              {userPlan !== 'free' && (
                <Button variant="outline">
                  Manage Billing
                </Button>
              )}
            </div>
          </div>

          {userPlan !== 'free' && (
            <div className="space-y-4">
              <h4 className="font-semibold">Payment History</h4>
              <div className="space-y-2">
                {[
                  { date: '2024-06-01', amount: userPlan === 'premium' ? 99000 : 299000, status: 'Paid' },
                  { date: '2024-05-01', amount: userPlan === 'premium' ? 99000 : 299000, status: 'Paid' },
                  { date: '2024-04-01', amount: userPlan === 'premium' ? 99000 : 299000, status: 'Paid' }
                ].map((payment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">{getPlanInfo().name}</p>
                      <p className="text-sm text-muted-foreground">{payment.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                        }).format(payment.amount)}
                      </p>
                      <Badge variant="default">{payment.status}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notifications Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications via email
              </p>
            </div>
            <Switch
              checked={settings.notifications.email}
              onCheckedChange={(checked) => 
                setSettings({
                  ...settings,
                  notifications: {...settings.notifications, email: checked}
                })
              }
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label>Push Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive push notifications in browser
              </p>
            </div>
            <Switch
              checked={settings.notifications.push}
              onCheckedChange={(checked) => 
                setSettings({
                  ...settings,
                  notifications: {...settings.notifications, push: checked}
                })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Security Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security
              </p>
            </div>
            <Switch
              checked={settings.security.twoFactor}
              onCheckedChange={(checked) => 
                setSettings({
                  ...settings,
                  security: {...settings.security, twoFactor: checked}
                })
              }
            />
          </div>
          
          <div className="space-y-2">
            <Label>Session Timeout (minutes)</Label>
            <Input
              type="number"
              value={settings.security.sessionTimeout}
              onChange={(e) => 
                setSettings({
                  ...settings,
                  security: {...settings.security, sessionTimeout: parseInt(e.target.value)}
                })
              }
              className="w-32"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSaveSettings}>
          <Save className="w-4 h-4 mr-2" />
          Save All Settings
        </Button>
      </div>

      {/* Paywall Modal */}
      <PaywallModal
        open={showPaywall}
        onOpenChange={setShowPaywall}
        currentPlan={userPlan}
        onUpgrade={handleUpgrade}
      />
    </div>
  );
};
