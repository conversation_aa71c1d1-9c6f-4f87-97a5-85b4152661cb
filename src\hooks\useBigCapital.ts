
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { bigCapitalService, BigCapitalInvoice } from '@/services/bigCapitalService';
import { toast } from 'sonner';

export const useBigCapital = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState<BigCapitalInvoice[]>([]);
  const [vendorBills, setVendorBills] = useState<any[]>([]);

  const fetchInvoices = async () => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    setLoading(true);
    try {
      console.log('Fetching invoices...');
      const fetchedInvoices = await bigCapitalService.getInvoices(user.id);
      console.log('Fetched invoices:', fetchedInvoices);
      setInvoices(fetchedInvoices);
      
      if (fetchedInvoices.length === 0) {
        toast.info('No invoices found in BigCapital');
      } else {
        toast.success(`${fetchedInvoices.length} invoice(s) loaded from BigCapital`);
      }
    } catch (error) {
      console.error('Error fetching invoices:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      // Show more specific error messages
      if (errorMessage.includes('Unable to connect')) {
        toast.error('Cannot connect to BigCapital API', {
          description: 'The API may be down or have CORS restrictions. Please contact support.'
        });
      } else if (errorMessage.includes('credentials not found')) {
        toast.error('BigCapital credentials missing', {
          description: 'Please configure your BigCapital credentials.'
        });
      } else {
        toast.error(`Failed to fetch invoices: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchVendorBills = async () => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    setLoading(true);
    try {
      console.log('Fetching vendor bills...');
      const fetchedBills = await bigCapitalService.getVendorBills(user.id);
      console.log('Fetched vendor bills:', fetchedBills);
      setVendorBills(fetchedBills);
      
      if (fetchedBills.length === 0) {
        toast.info('No vendor bills found in BigCapital');
      } else {
        toast.success(`${fetchedBills.length} vendor bill(s) loaded from BigCapital`);
      }
    } catch (error) {
      console.error('Error fetching vendor bills:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (errorMessage.includes('Unable to connect')) {
        toast.error('Cannot connect to BigCapital API for vendor bills', {
          description: 'The API may be down or have CORS restrictions.'
        });
      } else if (errorMessage.includes('credentials not found')) {
        toast.error('BigCapital credentials missing', {
          description: 'Please configure your BigCapital credentials.'
        });
      } else {
        toast.error(`Failed to fetch vendor bills: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const testAuthentication = async () => {
    if (!user) {
      toast.error('User not authenticated');
      return false;
    }

    setLoading(true);
    try {
      console.log('Testing BigCapital authentication...');
      await bigCapitalService.authenticateAndGetToken(user.id);
      toast.success('BigCapital authentication successful');
      return true;
    } catch (error) {
      console.error('Error testing authentication:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (errorMessage.includes('Unable to connect')) {
        toast.error('Cannot connect to BigCapital API', {
          description: 'The API may be down or have CORS restrictions.'
        });
      } else {
        toast.error(`Authentication failed: ${errorMessage}`);
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    invoices,
    vendorBills,
    fetchInvoices,
    fetchVendorBills,
    testAuthentication,
  };
};
