import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for journal entries from API
interface JournalEntry {
  id: number;
  journal_number: string;
  journal_type: string;
  date: string;
  formatted_date: string;
  reference: string | null;
  description: string | null;
  currency_code: string;
  total_debit: number;
  total_credit: number;
  total_debit_formatted: string;
  total_credit_formatted: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  entries: Array<{
    id: number;
    account_id: number;
    account: {
      id: number;
      name: string;
      code: string;
      account_type: string;
      account_normal: string;
    };
    debit: number;
    credit: number;
    debit_formatted: string;
    credit_formatted: string;
    description: string | null;
    contact_id: number | null;
    contact: {
      id: number;
      display_name: string;
      contact_type: string;
    } | null;
  }>;
}

interface JournalDetailDialogProps {
  journalEntry: JournalEntry | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const JournalDetailDialog: React.FC<JournalDetailDialogProps> = ({
  journalEntry,
  open,
  onOpenChange,
}) => {
  if (!journalEntry) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getJournalTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'general':
      case 'umum':
        return 'bg-blue-100 text-blue-800';
      case 'adjustment':
      case 'penyesuaian':
        return 'bg-purple-100 text-purple-800';
      case 'closing':
      case 'penutup':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Detail Jurnal {journalEntry.journal_number}</span>
            <Badge className={getJournalTypeColor(journalEntry.journal_type)}>
              {journalEntry.journal_type}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Journal Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Jurnal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Nomor Jurnal:</span>
                  <p className="font-semibold">{journalEntry.journal_number}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Tipe Jurnal:</span>
                  <p className="font-semibold">{journalEntry.journal_type}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Tanggal:</span>
                  <p>{journalEntry.formatted_date || formatDate(journalEntry.date)}</p>
                </div>
                {journalEntry.reference && (
                  <div>
                    <span className="text-sm text-muted-foreground">Referensi:</span>
                    <p>{journalEntry.reference}</p>
                  </div>
                )}
                {journalEntry.description && (
                  <div>
                    <span className="text-sm text-muted-foreground">Deskripsi:</span>
                    <p>{journalEntry.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ringkasan Saldo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Total Debit:</span>
                  <p className="text-2xl font-bold text-green-600">
                    {journalEntry.total_debit_formatted || formatCurrency(journalEntry.total_debit)}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Total Kredit:</span>
                  <p className="text-2xl font-bold text-red-600">
                    {journalEntry.total_credit_formatted || formatCurrency(journalEntry.total_credit)}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Selisih:</span>
                  <p className={`font-semibold ${
                    journalEntry.total_debit === journalEntry.total_credit 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {journalEntry.total_debit === journalEntry.total_credit 
                      ? 'Seimbang ✓' 
                      : `Tidak Seimbang (${formatCurrency(Math.abs(journalEntry.total_debit - journalEntry.total_credit))})`
                    }
                  </p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Mata Uang:</span>
                  <p className="font-semibold">{journalEntry.currency_code}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Journal Entries */}
          {journalEntry.entries && journalEntry.entries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Entri Jurnal</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Akun</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Kontak</TableHead>
                      <TableHead className="text-right">Debit</TableHead>
                      <TableHead className="text-right">Kredit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {journalEntry.entries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">
                          <div>
                            <p className="font-semibold">{entry.account.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {entry.account.code} - {entry.account.account_type}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {entry.description || '-'}
                        </TableCell>
                        <TableCell>
                          {entry.contact ? (
                            <div>
                              <p className="font-medium">{entry.contact.display_name}</p>
                              <p className="text-sm text-muted-foreground">{entry.contact.contact_type}</p>
                            </div>
                          ) : '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          {entry.debit > 0 ? (
                            <span className="font-semibold text-green-600">
                              {entry.debit_formatted || formatCurrency(entry.debit)}
                            </span>
                          ) : '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          {entry.credit > 0 ? (
                            <span className="font-semibold text-red-600">
                              {entry.credit_formatted || formatCurrency(entry.credit)}
                            </span>
                          ) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                
                {/* Totals Row */}
                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between items-center font-bold">
                    <span>Total:</span>
                    <div className="flex gap-8">
                      <span className="text-green-600">
                        {journalEntry.total_debit_formatted || formatCurrency(journalEntry.total_debit)}
                      </span>
                      <span className="text-red-600">
                        {journalEntry.total_credit_formatted || formatCurrency(journalEntry.total_credit)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Dibuat pada:</span>
                <p>{journalEntry.formatted_created_at || formatDate(journalEntry.created_at)}</p>
              </div>
              {journalEntry.updated_at && (
                <div>
                  <span className="text-sm text-muted-foreground">Diperbarui pada:</span>
                  <p>{formatDate(journalEntry.updated_at)}</p>
                </div>
              )}
              <div>
                <span className="text-sm text-muted-foreground">ID Jurnal:</span>
                <p className="font-mono text-sm">{journalEntry.id}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Jumlah Entri:</span>
                <p>{journalEntry.entries?.length || 0} entri</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
