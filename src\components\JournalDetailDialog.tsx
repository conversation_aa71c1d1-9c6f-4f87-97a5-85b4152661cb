import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for journal entries from API (updated to match actual response)
interface JournalEntry {
  id: number;
  journal_number: string;
  journal_type: string | null;
  date: string;
  formatted_date: string;
  reference: string | null;
  description: string | null;
  currency_code: string;
  amount: number;
  amount_formatted: string;
  formatted_amount: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  published_at: string;
  formatted_published_at: string;
  is_published: boolean;
  user_id: number;
  branch_id: number | null;
  attachment_file: string | null;
  exchange_rate: number;
  entries: Array<{
    id: number;
    account_id: number;
    contact_id: number | null;
    debit: number;
    credit: number;
    index: number;
    note: string | null;
    manual_journal_id: number;
    branch_id: number | null;
    project_id: number | null;
    account: {
      id: number;
      name: string;
      slug: string;
      code: string | null;
      account_type: string;
      account_type_label: string;
      account_parent_type: string;
      account_root_type: string;
      account_normal: string;
      account_normal_formatted: string;
      description: string | null;
      active: number;
      amount: number;
      currency_code: string;
      is_balance_sheet_account: boolean;
      is_pl_sheet: boolean;
      is_system_account: number;
    };
  }>;
}

interface JournalDetailDialogProps {
  journalEntry: JournalEntry | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const JournalDetailDialog: React.FC<JournalDetailDialogProps> = ({
  journalEntry,
  open,
  onOpenChange,
}) => {
  if (!journalEntry) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getJournalTypeColor = (type: string | null) => {
    if (!type) return 'bg-gray-100 text-gray-800';

    switch (type.toLowerCase()) {
      case 'general':
      case 'umum':
        return 'bg-blue-100 text-blue-800';
      case 'adjustment':
      case 'penyesuaian':
        return 'bg-purple-100 text-purple-800';
      case 'closing':
      case 'penutup':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Detail Jurnal {journalEntry.journal_number}</span>
            {journalEntry.journal_type && (
              <Badge className={getJournalTypeColor(journalEntry.journal_type)}>
                {journalEntry.journal_type}
              </Badge>
            )}
            <Badge className={journalEntry.is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
              {journalEntry.is_published ? 'Published' : 'Draft'}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Journal Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Jurnal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Nomor Jurnal:</span>
                  <p className="font-semibold">{journalEntry.journal_number}</p>
                </div>
                {journalEntry.journal_type && (
                  <div>
                    <span className="text-sm text-muted-foreground">Tipe Jurnal:</span>
                    <p className="font-semibold">{journalEntry.journal_type}</p>
                  </div>
                )}
                <div>
                  <span className="text-sm text-muted-foreground">Tanggal:</span>
                  <p>{journalEntry.formatted_date || formatDate(journalEntry.date)}</p>
                </div>
                {journalEntry.reference && (
                  <div>
                    <span className="text-sm text-muted-foreground">Referensi:</span>
                    <p>{journalEntry.reference}</p>
                  </div>
                )}
                {journalEntry.description && (
                  <div>
                    <span className="text-sm text-muted-foreground">Deskripsi:</span>
                    <p>{journalEntry.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ringkasan Saldo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {(() => {
                  const totalDebit = journalEntry.entries.reduce((sum, entry) => sum + (Number(entry.debit) || 0), 0);
                  const totalCredit = journalEntry.entries.reduce((sum, entry) => sum + (Number(entry.credit) || 0), 0);
                  const isBalanced = totalDebit === totalCredit;

                  return (
                    <>
                      <div>
                        <span className="text-sm text-muted-foreground">Total Debit:</span>
                        <p className="text-2xl font-bold text-green-600">
                          {formatCurrency(totalDebit)}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Total Kredit:</span>
                        <p className="text-2xl font-bold text-red-600">
                          {formatCurrency(totalCredit)}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Selisih:</span>
                        <p className={`font-semibold ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                          {isBalanced
                            ? 'Seimbang ✓'
                            : `Tidak Seimbang (${formatCurrency(Math.abs(totalDebit - totalCredit))})`
                          }
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Amount Jurnal:</span>
                        <p className="text-lg font-bold text-blue-600">
                          {journalEntry.amount_formatted || formatCurrency(journalEntry.amount)}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Mata Uang:</span>
                        <p className="font-semibold">{journalEntry.currency_code}</p>
                      </div>
                    </>
                  );
                })()}
              </CardContent>
            </Card>
          </div>

          {/* Journal Entries */}
          {journalEntry.entries && journalEntry.entries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Entri Jurnal</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Akun</TableHead>
                      <TableHead>Note</TableHead>
                      <TableHead>Index</TableHead>
                      <TableHead className="text-right">Debit</TableHead>
                      <TableHead className="text-right">Kredit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {journalEntry.entries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">
                          <div>
                            <p className="font-semibold">{entry.account.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {entry.account.code && `${entry.account.code} - `}{entry.account.account_type_label}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {entry.account.account_parent_type} • {entry.account.account_normal_formatted}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {entry.note || '-'}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm font-mono">{entry.index}</span>
                        </TableCell>
                        <TableCell className="text-right">
                          {entry.debit > 0 ? (
                            <span className="font-semibold text-green-600">
                              {formatCurrency(entry.debit)}
                            </span>
                          ) : '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          {entry.credit > 0 ? (
                            <span className="font-semibold text-red-600">
                              {formatCurrency(entry.credit)}
                            </span>
                          ) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                
                {/* Totals Row */}
                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between items-center font-bold">
                    <span>Total:</span>
                    <div className="flex gap-8">
                      <span className="text-green-600">
                        {formatCurrency(journalEntry.entries.reduce((sum, entry) => sum + (Number(entry.debit) || 0), 0))}
                      </span>
                      <span className="text-red-600">
                        {formatCurrency(journalEntry.entries.reduce((sum, entry) => sum + (Number(entry.credit) || 0), 0))}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Dibuat pada:</span>
                <p>{journalEntry.formatted_created_at || formatDate(journalEntry.created_at)}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Dipublish pada:</span>
                <p>{journalEntry.formatted_published_at || formatDate(journalEntry.published_at)}</p>
              </div>
              {journalEntry.updated_at && (
                <div>
                  <span className="text-sm text-muted-foreground">Diperbarui pada:</span>
                  <p>{formatDate(journalEntry.updated_at)}</p>
                </div>
              )}
              <div>
                <span className="text-sm text-muted-foreground">Status:</span>
                <p className="font-semibold">{journalEntry.is_published ? 'Published' : 'Draft'}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">ID Jurnal:</span>
                <p className="font-mono text-sm">{journalEntry.id}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">User ID:</span>
                <p className="font-mono text-sm">{journalEntry.user_id}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Exchange Rate:</span>
                <p>{journalEntry.exchange_rate}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Jumlah Entri:</span>
                <p>{journalEntry.entries?.length || 0} entri</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
