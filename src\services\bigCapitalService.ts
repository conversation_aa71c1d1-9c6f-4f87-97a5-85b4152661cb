import { supabase } from '@/integrations/supabase/client';

export interface BigCapitalAuthResponse {
  'x-access-token': string;
  'organization-id': string;
  [key: string]: any;
}

export interface BigCapitalInvoice {
  id: string;
  invoice_number: string;
  customer_name: string;
  amount: number;
  status: string;
  due_date: string;
  created_at: string;
  [key: string]: any;
}

class BigCapitalService {
  private webhookUrl = 'https://wabot-n8n.libslm.easypanel.host/webhook/0380c66e-57ea-4423-9981-641beda8a6f4';
  private apiBaseUrl = 'https://wabot-bigcapital.libslm.easypanel.host';

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const { data: { session } } = await supabase.auth.getSession();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    return headers;
  }

  async authenticateAndGetToken(userId: string): Promise<{ access_token: string; organization_id: string } | null> {
    try {
      console.log('Authenticating with BigCapital webhook for user:', userId);
      
      // Get user credentials from database
      const { data: credentials, error: credError } = await supabase
        .from('bigcapital_auth')
        .select('credential_email, credential_password')
        .eq('user_id', userId)
        .single();

      if (credError || !credentials) {
        console.error('Failed to get credentials:', credError);
        throw new Error('User credentials not found. Please check if BigCapital credentials are properly configured.');
      }

      console.log('Attempting to authenticate with webhook using:', credentials.credential_email);

      const headers = await this.getAuthHeaders();

      // Send authentication request to webhook
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          action: 'authenticate',
          credential: credentials.credential_email,
          password: credentials.credential_password,
        }),
      });

      console.log('Webhook authentication response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Authentication failed response:', errorText);
        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);
      }

      const authData: BigCapitalAuthResponse = await response.json();
      console.log('Authentication successful:', authData);

      if (!authData['x-access-token'] || !authData['organization-id']) {
        throw new Error('Invalid authentication response: missing x-access-token or organization-id');
      }

      return {
        access_token: authData['x-access-token'],
        organization_id: authData['organization-id'],
      };
    } catch (error) {
      console.error('BigCapital authentication error:', error);
      
      // Check if it's a network error
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Unable to connect to BigCapital webhook. This could be due to:\n1. Network connectivity issues\n2. Webhook server is down\n3. CORS restrictions');
      }
      
      throw error;
    }
  }

  async getInvoices(userId: string): Promise<BigCapitalInvoice[]> {
    try {
      console.log('Fetching invoices for user:', userId);
      
      // Get fresh authentication token
      const auth = await this.authenticateAndGetToken(userId);
      
      if (!auth) {
        throw new Error('Failed to authenticate with BigCapital');
      }

      console.log('Using auth with organization_id:', auth.organization_id);

      const headers = await this.getAuthHeaders();
      // Override with BigCapital specific headers
      headers['x-access-token'] = auth.access_token;
      headers['organization-id'] = auth.organization_id;

      // Use GET method for fetching invoices
      const response = await fetch(`${this.apiBaseUrl}/api/sales/invoices`, {
        method: 'GET',
        headers,
      });

      console.log('Invoices response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch invoices: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Invoices fetched successfully:', data);
      
      return data.invoices || data.data || data || [];
    } catch (error) {
      console.error('Error fetching invoices:', error);
      
      // Check if it's a network error
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Unable to connect to BigCapital API. Please check if the service is accessible.');
      }
      
      throw error;
    }
  }

  async getVendorBills(userId: string): Promise<any[]> {
    try {
      console.log('Fetching vendor bills for user:', userId);
      
      // Get Supabase auth headers (JWT token)
      const headers = await this.getAuthHeaders();

      console.log('Vendor bills request headers:', {
        'Authorization': headers['Authorization'] ? 'Bearer [JWT token]' : 'Not set',
        'Content-Type': headers['Content-Type'],
        'Accept': headers['Accept']
      });

      // Use the new API endpoint with only Supabase JWT auth
      const response = await fetch('https://wabot-n8n.libslm.easypanel.host/webhook/transync/purchases/list', {
        method: 'GET',
        headers,
      });

      console.log('Vendor bills response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Vendor bills error response:', errorText);
        throw new Error(`Failed to fetch vendor bills: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Vendor bills fetched successfully:', data);
      
      return data.bills || data.data || data || [];
    } catch (error) {
      console.error('Error fetching vendor bills:', error);
      
      // Check if it's a network error
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Unable to connect to BigCapital API for vendor bills. Please check if the service is accessible.');
      }
      
      throw error;
    }
  }
}

export const bigCapitalService = new BigCapitalService();
