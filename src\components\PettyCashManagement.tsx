import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Wallet,
  Plus,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  User,
  FileText,
  Download,
  Filter,
  Search,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface PettyCashTransaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  date: string;
  createdBy: string;
  receipt?: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface PettyCashManagementProps {
  userPlan: 'free' | 'premium' | 'enterprise';
  userRole: 'admin' | 'general' | 'petty_cash' | 'staff';
}

const categories = {
  expense: [
    'Transportasi',
    'Konsumsi',
    'ATK (Alat Tulis Kantor)',
    'Komunikasi',
    'Maintenance',
    'Lain-lain'
  ],
  income: [
    'Pengembalian',
    'Bonus',
    'Lain-lain'
  ]
};

export const PettyCashManagement: React.FC<PettyCashManagementProps> = ({
  userPlan,
  userRole
}) => {
  const [transactions, setTransactions] = useState<PettyCashTransaction[]>([
    {
      id: '1',
      type: 'expense',
      amount: 50000,
      description: 'Bensin motor untuk pengiriman dokumen',
      category: 'Transportasi',
      date: '2024-06-19',
      createdBy: 'John Doe',
      status: 'approved'
    },
    {
      id: '2',
      type: 'expense',
      amount: 25000,
      description: 'Makan siang meeting dengan klien',
      category: 'Konsumsi',
      date: '2024-06-18',
      createdBy: 'Jane Smith',
      status: 'pending'
    },
    {
      id: '3',
      type: 'income',
      amount: 100000,
      description: 'Pengembalian uang muka project',
      category: 'Pengembalian',
      date: '2024-06-17',
      createdBy: 'Bob Wilson',
      status: 'approved'
    }
  ]);

  const [showAddTransaction, setShowAddTransaction] = useState(false);
  const [newTransaction, setNewTransaction] = useState({
    type: 'expense' as 'income' | 'expense',
    amount: '',
    description: '',
    category: '',
    date: new Date().toISOString().split('T')[0]
  });

  const [filter, setFilter] = useState({
    type: 'all',
    status: 'all',
    search: ''
  });

  const hasAccess = userPlan !== 'free';
  const canManage = hasAccess && (userRole === 'admin' || userRole === 'petty_cash');
  const canVerify = hasAccess && (userRole === 'staff' || userRole === 'admin');
  const canApprove = hasAccess && userRole === 'admin';
  const hasApprovalFeature = userPlan === 'premium' || userPlan === 'enterprise';

  const totalBalance = transactions
    .filter(t => t.status === 'approved')
    .reduce((sum, t) => sum + (t.type === 'income' ? t.amount : -t.amount), 0);

  const totalIncome = transactions
    .filter(t => t.type === 'income' && t.status === 'approved')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpense = transactions
    .filter(t => t.type === 'expense' && t.status === 'approved')
    .reduce((sum, t) => sum + t.amount, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleAddTransaction = () => {
    if (!newTransaction.amount || !newTransaction.description || !newTransaction.category) {
      toast.error('Semua field harus diisi');
      return;
    }

    const transaction: PettyCashTransaction = {
      id: Date.now().toString(),
      type: newTransaction.type,
      amount: parseInt(newTransaction.amount),
      description: newTransaction.description,
      category: newTransaction.category,
      date: newTransaction.date,
      createdBy: 'Current User',
      status: canManage ? 'approved' : 'pending'
    };

    setTransactions([transaction, ...transactions]);
    setNewTransaction({
      type: 'expense',
      amount: '',
      description: '',
      category: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowAddTransaction(false);
    toast.success('Transaksi berhasil ditambahkan!');
  };

  const handleStatusChange = (id: string, status: PettyCashTransaction['status']) => {
    setTransactions(transactions.map(t =>
      t.id === id ? { ...t, status } : t
    ));
    toast.success(`Status transaksi berhasil diubah ke ${status}`);
  };

  // Approval workflow functions
  const handleVerify = (transactionId: string) => {
    if (!canVerify) {
      toast.error('Hanya staff yang dapat melakukan verifikasi');
      return;
    }
    toast.success('Transaksi kas kecil berhasil diverifikasi');
  };

  const handleApprove = (transactionId: string) => {
    if (!canApprove) {
      toast.error('Hanya administrator yang dapat melakukan approval');
      return;
    }
    handleStatusChange(transactionId, 'approved');
  };

  const handleReject = (transactionId: string) => {
    if (!canApprove) {
      toast.error('Hanya administrator yang dapat menolak transaksi');
      return;
    }
    handleStatusChange(transactionId, 'rejected');
  };

  const filteredTransactions = transactions.filter(t => {
    if (filter.type !== 'all' && t.type !== filter.type) return false;
    if (filter.status !== 'all' && t.status !== filter.status) return false;
    if (filter.search && !t.description.toLowerCase().includes(filter.search.toLowerCase())) return false;
    return true;
  });

  const getStatusBadge = (status: PettyCashTransaction['status']) => {
    const config = {
      pending: { label: 'Pending', variant: 'secondary' as const },
      approved: { label: 'Disetujui', variant: 'default' as const },
      rejected: { label: 'Ditolak', variant: 'destructive' as const }
    };
    return config[status];
  };

  if (!hasAccess) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-8 text-center">
          <Wallet className="w-16 h-16 mx-auto mb-4 text-yellow-600" />
          <h3 className="text-xl font-semibold text-yellow-800 mb-2">
            Petty Cash Management
          </h3>
          <p className="text-yellow-700 mb-4">
            Fitur ini hanya tersedia untuk pengguna Premium dan Enterprise
          </p>
          <Button variant="outline">
            Upgrade ke Premium
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Wallet className="w-8 h-8 text-blue-500" />
          <div>
            <h2 className="text-2xl font-bold">Petty Cash Management</h2>
            <p className="text-muted-foreground">
              Kelola kas kecil dan pengeluaran operasional
            </p>
          </div>
        </div>
        
        {canManage && (
          <Dialog open={showAddTransaction} onOpenChange={setShowAddTransaction}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Transaksi
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Tambah Transaksi Kas Kecil</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Jenis Transaksi</Label>
                  <Select 
                    value={newTransaction.type} 
                    onValueChange={(value) => setNewTransaction({...newTransaction, type: value as 'income' | 'expense', category: ''})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="expense">
                        <div className="flex items-center gap-2">
                          <TrendingDown className="w-4 h-4 text-red-500" />
                          Pengeluaran
                        </div>
                      </SelectItem>
                      <SelectItem value="income">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-green-500" />
                          Pemasukan
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Kategori</Label>
                  <Select 
                    value={newTransaction.category} 
                    onValueChange={(value) => setNewTransaction({...newTransaction, category: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih kategori" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories[newTransaction.type].map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Jumlah</Label>
                  <Input
                    type="number"
                    value={newTransaction.amount}
                    onChange={(e) => setNewTransaction({...newTransaction, amount: e.target.value})}
                    placeholder="Masukkan jumlah"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Deskripsi</Label>
                  <Textarea
                    value={newTransaction.description}
                    onChange={(e) => setNewTransaction({...newTransaction, description: e.target.value})}
                    placeholder="Deskripsi transaksi"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Tanggal</Label>
                  <Input
                    type="date"
                    value={newTransaction.date}
                    onChange={(e) => setNewTransaction({...newTransaction, date: e.target.value})}
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button variant="outline" onClick={() => setShowAddTransaction(false)} className="flex-1">
                    Batal
                  </Button>
                  <Button onClick={handleAddTransaction} className="flex-1">
                    Simpan Transaksi
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Wallet className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Saldo Kas</p>
                <p className={`text-xl font-bold ${totalBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(totalBalance)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Pemasukan</p>
                <p className="text-xl font-bold text-green-600">
                  {formatCurrency(totalIncome)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <TrendingDown className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Pengeluaran</p>
                <p className="text-xl font-bold text-red-600">
                  {formatCurrency(totalExpense)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FileText className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Transaksi</p>
                <p className="text-xl font-bold">
                  {transactions.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Cari transaksi..."
                value={filter.search}
                onChange={(e) => setFilter({...filter, search: e.target.value})}
                className="w-64"
              />
            </div>
            
            <Select value={filter.type} onValueChange={(value) => setFilter({...filter, type: value})}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Jenis</SelectItem>
                <SelectItem value="income">Pemasukan</SelectItem>
                <SelectItem value="expense">Pengeluaran</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filter.status} onValueChange={(value) => setFilter({...filter, status: value})}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Disetujui</SelectItem>
                <SelectItem value="rejected">Ditolak</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Transactions List */}
      <div className="space-y-3">
        {filteredTransactions.map((transaction) => (
          <Card key={transaction.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`p-2 rounded-lg ${
                    transaction.type === 'income' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    {transaction.type === 'income' ? (
                      <TrendingUp className="w-5 h-5 text-green-600" />
                    ) : (
                      <TrendingDown className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{transaction.description}</h3>
                      <Badge {...getStatusBadge(transaction.status)}>
                        {getStatusBadge(transaction.status).label}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{transaction.category}</span>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(transaction.date).toLocaleDateString('id-ID')}
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {transaction.createdBy}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  <div className={`text-xl font-bold ${
                    transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </div>

                  {/* Two-step Approval for Premium/Enterprise */}
                  {hasApprovalFeature && transaction.status === 'pending' && (
                    <div className="flex gap-1">
                      {canVerify && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleVerify(transaction.id)}
                          className="text-xs h-6 px-2"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Verify
                        </Button>
                      )}
                      {canApprove && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleApprove(transaction.id)}
                            className="text-xs h-6 px-2"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleReject(transaction.id)}
                            className="text-xs h-6 px-2"
                          >
                            <XCircle className="w-3 h-3 mr-1" />
                            Reject
                          </Button>
                        </>
                      )}
                    </div>
                  )}

                  {/* Legacy approval for non-premium plans */}
                  {!hasApprovalFeature && canManage && transaction.status === 'pending' && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStatusChange(transaction.id, 'approved')}
                      >
                        Setujui
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleStatusChange(transaction.id, 'rejected')}
                      >
                        Tolak
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTransactions.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Wallet className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="font-semibold mb-2">Belum ada transaksi</h3>
            <p className="text-muted-foreground mb-4">
              Mulai tambahkan transaksi kas kecil Anda
            </p>
            {canManage && (
              <Button onClick={() => setShowAddTransaction(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Transaksi Pertama
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
