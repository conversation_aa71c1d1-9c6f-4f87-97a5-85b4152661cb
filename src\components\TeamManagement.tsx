import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Users, 
  Plus, 
  Mail, 
  Shield, 
  Edit, 
  Trash2, 
  Crown, 
  Building2,
  ShoppingCart,
  DollarSign,
  FileText,
  Wallet
} from 'lucide-react';
import { toast } from 'sonner';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash';
  status: 'active' | 'pending' | 'inactive';
  joinedAt: string;
  avatar?: string;
}

interface TeamManagementProps {
  userPlan: 'free' | 'premium' | 'enterprise';
  maxMembers: number;
}

const roleConfig = {
  admin: {
    label: 'Administrator',
    description: 'Full access to all features',
    icon: <Shield className="w-4 h-4" />,
    color: 'bg-red-500'
  },
  purchase: {
    label: 'Purchase Manager',
    description: 'Manage vendor bills and purchases',
    icon: <ShoppingCart className="w-4 h-4" />,
    color: 'bg-blue-500'
  },
  sales: {
    label: 'Sales Manager',
    description: 'Manage customer invoices and sales',
    icon: <DollarSign className="w-4 h-4" />,
    color: 'bg-green-500'
  },
  general: {
    label: 'General Transaction',
    description: 'Manage general transactions and reports',
    icon: <FileText className="w-4 h-4" />,
    color: 'bg-purple-500'
  },
  petty_cash: {
    label: 'Petty Cash Manager',
    description: 'Manage petty cash transactions',
    icon: <Wallet className="w-4 h-4" />,
    color: 'bg-orange-500'
  }
};

export const TeamManagement: React.FC<TeamManagementProps> = ({
  userPlan,
  maxMembers
}) => {
  const [members, setMembers] = useState<TeamMember[]>([
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      joinedAt: '2024-01-15',
      avatar: 'JD'
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'purchase',
      status: 'active',
      joinedAt: '2024-02-01',
      avatar: 'JS'
    },
    {
      id: '3',
      name: 'Bob Wilson',
      email: '<EMAIL>',
      role: 'petty_cash',
      status: 'pending',
      joinedAt: '2024-03-10',
      avatar: 'BW'
    }
  ]);

  const [showAddMember, setShowAddMember] = useState(false);
  const [newMember, setNewMember] = useState({
    name: '',
    email: '',
    role: 'general' as TeamMember['role']
  });

  const canAddMembers = userPlan !== 'free' && (
    userPlan === 'enterprise' || members.length < maxMembers
  );

  const handleAddMember = () => {
    if (!newMember.name || !newMember.email) {
      toast.error('Nama dan email harus diisi');
      return;
    }

    if (!canAddMembers) {
      toast.error('Batas maksimal anggota tim telah tercapai');
      return;
    }

    const member: TeamMember = {
      id: Date.now().toString(),
      name: newMember.name,
      email: newMember.email,
      role: newMember.role,
      status: 'pending',
      joinedAt: new Date().toISOString().split('T')[0],
      avatar: newMember.name.split(' ').map(n => n[0]).join('').toUpperCase()
    };

    setMembers([...members, member]);
    setNewMember({ name: '', email: '', role: 'general' });
    setShowAddMember(false);
    toast.success('Undangan berhasil dikirim!');
  };

  const handleRemoveMember = (id: string) => {
    setMembers(members.filter(m => m.id !== id));
    toast.success('Anggota berhasil dihapus');
  };

  const handleRoleChange = (id: string, newRole: TeamMember['role']) => {
    setMembers(members.map(m => 
      m.id === id ? { ...m, role: newRole } : m
    ));
    toast.success('Role berhasil diubah');
  };

  const getStatusBadge = (status: TeamMember['status']) => {
    const config = {
      active: { label: 'Aktif', variant: 'default' as const },
      pending: { label: 'Pending', variant: 'secondary' as const },
      inactive: { label: 'Nonaktif', variant: 'destructive' as const }
    };
    return config[status];
  };

  const getPlanIcon = () => {
    switch (userPlan) {
      case 'premium':
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 'enterprise':
        return <Building2 className="w-5 h-5 text-blue-500" />;
      default:
        return <Users className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getPlanIcon()}
          <div>
            <h2 className="text-2xl font-bold">Team Management</h2>
            <p className="text-muted-foreground">
              Kelola anggota tim dan role mereka
            </p>
          </div>
        </div>
        
        <Dialog open={showAddMember} onOpenChange={setShowAddMember}>
          <DialogTrigger asChild>
            <Button disabled={!canAddMembers}>
              <Plus className="w-4 h-4 mr-2" />
              Tambah Anggota
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Tambah Anggota Tim</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Lengkap</Label>
                <Input
                  id="name"
                  value={newMember.name}
                  onChange={(e) => setNewMember({...newMember, name: e.target.value})}
                  placeholder="Masukkan nama lengkap"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newMember.email}
                  onChange={(e) => setNewMember({...newMember, email: e.target.value})}
                  placeholder="Masukkan email"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select 
                  value={newMember.role} 
                  onValueChange={(value) => setNewMember({...newMember, role: value as TeamMember['role']})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(roleConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-center gap-2">
                          {config.icon}
                          <div>
                            <div className="font-medium">{config.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {config.description}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex gap-3 pt-4">
                <Button variant="outline" onClick={() => setShowAddMember(false)} className="flex-1">
                  Batal
                </Button>
                <Button onClick={handleAddMember} className="flex-1">
                  Kirim Undangan
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Plan Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getPlanIcon()}
              <div>
                <h3 className="font-semibold capitalize">{userPlan} Plan</h3>
                <p className="text-sm text-muted-foreground">
                  {userPlan === 'enterprise' 
                    ? 'Unlimited team members' 
                    : `${members.length}/${maxMembers} anggota tim`
                  }
                </p>
              </div>
            </div>
            
            {userPlan === 'free' && (
              <Button variant="outline" size="sm">
                Upgrade Plan
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Members List */}
      <div className="grid gap-4">
        {members.map((member) => (
          <Card key={member.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className={roleConfig[member.role].color}>
                      {member.avatar}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{member.name}</h3>
                      <Badge {...getStatusBadge(member.status)}>
                        {getStatusBadge(member.status).label}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Mail className="w-3 h-3" />
                      {member.email}
                    </div>
                    <div className="flex items-center gap-2">
                      {roleConfig[member.role].icon}
                      <span className="text-sm font-medium">
                        {roleConfig[member.role].label}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Select 
                    value={member.role} 
                    onValueChange={(value) => handleRoleChange(member.id, value as TeamMember['role'])}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(roleConfig).map(([key, config]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            {config.icon}
                            {config.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  {member.role !== 'admin' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveMember(member.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {members.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="font-semibold mb-2">Belum ada anggota tim</h3>
            <p className="text-muted-foreground mb-4">
              Tambahkan anggota tim untuk mulai berkolaborasi
            </p>
            <Button onClick={() => setShowAddMember(true)} disabled={!canAddMembers}>
              <Plus className="w-4 h-4 mr-2" />
              Tambah Anggota Pertama
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Upgrade Notice */}
      {userPlan === 'free' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Crown className="w-5 h-5 text-yellow-600" />
              <div>
                <h3 className="font-semibold text-yellow-800">
                  Upgrade untuk Team Management
                </h3>
                <p className="text-sm text-yellow-700">
                  Upgrade ke Premium atau Enterprise untuk menambah anggota tim dan mengakses fitur petty cash management
                </p>
              </div>
              <Button variant="outline" size="sm" className="ml-auto">
                Upgrade Sekarang
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
