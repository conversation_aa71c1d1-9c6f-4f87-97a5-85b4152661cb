
const WAHA_BASE_URL = 'https://wabot-waha.libslm.easypanel.host/api/transync_new';

export interface WhatsAppMessage {
  id: string;
  timestamp: number;
  from: string;
  fromMe: boolean;
  body: string;
  type: string;
  mediaUrl?: string;
}

export const wahaService = {
  async fetchMessages(phoneNumber: string, limit: number = 10): Promise<WhatsAppMessage[]> {
    try {
      const chatId = `${phoneNumber}@c.us`;
      const url = `${WAHA_BASE_URL}/chats/${encodeURIComponent(chatId)}/messages?downloadMedia=true&limit=${limit}`;
      
      console.log('Fetching WhatsApp messages from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('WhatsApp messages response:', data);
      
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Error fetching WhatsApp messages:', error);
      return [];
    }
  }
};
