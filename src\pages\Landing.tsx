import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle, List, FileText, Users, CheckCircle, ArrowRight, Star } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const Landing = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const features = [
    {
      icon: MessageCircle,
      title: 'Chat AI Keuangan',
      description: 'Kelola transaksi dengan mudah melalui chat AI yang cerdas'
    },
    {
      icon: List,
      title: 'Pelacakan Transaksi',
      description: 'Pantau semua transaksi keuangan dalam satu tempat'
    },
    {
      icon: FileText,
      title: 'Laporan Otomatis',
      description: 'Buat laporan keuangan profesional secara otomatis'
    },
    {
      icon: Users,
      title: 'Man<PERSON><PERSON><PERSON> Tim',
      description: '<PERSON><PERSON><PERSON> tim dan persetujuan dengan sistem yang terintegrasi'
    }
  ];

  const benefits = [
    'Hemat waktu dengan automasi AI',
    'Laporan keuangan yang akurat',
    'Kolaborasi tim yang efisien',
    'Interface yang mudah digunakan'
  ];

  const handleGetStarted = () => {
    if (user) {
      navigate('/app');
    } else {
      navigate('/auth');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex justify-between items-center">
          <div className="text-2xl font-bold text-primary">TranSync</div>
          <div className="flex gap-2">
            {user ? (
              <Button onClick={() => navigate('/app')} variant="outline">
                Buka Aplikasi
              </Button>
            ) : (
              <>
                <Button onClick={() => navigate('/auth')} variant="outline">
                  Masuk
                </Button>
                <Button onClick={() => navigate('/auth')}>
                  Daftar
                </Button>
              </>
            )}
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Revolusi Manajemen Keuangan dengan
            <span className="text-primary"> AI</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            TranSync menghadirkan solusi manajemen keuangan yang cerdas dengan teknologi AI. 
            Kelola transaksi, buat laporan, dan koordinasi tim dengan mudah.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-6" onClick={handleGetStarted}>
              {user ? 'Buka Aplikasi' : 'Mulai Gratis'}
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6">
              Lihat Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Fitur Unggulan TranSync
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Dilengkapi dengan fitur-fitur canggih untuk memudahkan pengelolaan keuangan bisnis Anda
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-6 h-6 text-primary" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Mengapa Memilih TranSync?
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
              <Button className="mt-8" size="lg" onClick={handleGetStarted}>
                Coba Sekarang
              </Button>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-primary mb-2">10,000+</div>
                <div className="text-gray-600 mb-4">Bisnis Telah Mempercayai</div>
                <div className="flex justify-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <div className="text-sm text-gray-600">Rating 4.9/5 dari pengguna</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Siap Meningkatkan Manajemen Keuangan Anda?
          </h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan bisnis yang telah merasakan kemudahan TranSync
          </p>
          <Button size="lg" variant="secondary" className="text-lg px-8 py-6" onClick={handleGetStarted}>
            {user ? 'Buka Aplikasi' : 'Mulai Gratis Hari Ini'}
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-xl font-bold mb-4">TranSync</div>
              <p className="text-gray-400">
                Solusi manajemen keuangan yang cerdas untuk bisnis modern
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Produk</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Chat AI</li>
                <li>Laporan Otomatis</li>
                <li>Manajemen Tim</li>
                <li>Integrasi WhatsApp</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Perusahaan</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Tentang Kami</li>
                <li>Karir</li>
                <li>Blog</li>
                <li>Kontak</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Dukungan</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Pusat Bantuan</li>
                <li>Dokumentasi</li>
                <li>Komunitas</li>
                <li>Status Layanan</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 TranSync. Semua hak dilindungi.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
