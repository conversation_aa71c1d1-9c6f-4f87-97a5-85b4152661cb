// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://pmcountrcoqanobyewlq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBtY291bnRyY29xYW5vYnlld2xxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5Nzc5MTMsImV4cCI6MjA2NTU1MzkxM30.-hHondA6quNTvMoGRuW2R4Bpxf6J_ZFKP0NMnwR3Krs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);