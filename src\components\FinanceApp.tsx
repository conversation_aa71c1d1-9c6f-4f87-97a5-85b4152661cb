
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChatTab } from './tabs/ChatTab';
import { TransactionsTab } from './tabs/TransactionsTab';
import { ReportsTab } from './tabs/ReportsTab';
import { TeamTab } from './tabs/TeamTab';
import { MessageCircle, List, FileText, Users, Home, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export const FinanceApp = () => {
  const [activeTab, setActiveTab] = useState('chats');
  const navigate = useNavigate();
  const { signOut } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success('Berhasil keluar!');
      navigate('/');
    } catch (error) {
      toast.error('Gagal keluar');
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4 border-b">
        <h1 className="text-xl font-bold">TranSync</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => navigate('/')}>
            <Home className="w-4 h-4 mr-2" />
            Beranda
          </Button>
          <Button variant="outline" size="sm" onClick={handleSignOut}>
            <LogOut className="w-4 h-4 mr-2" />
            Keluar
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full flex flex-col flex-1">
        {/* Main content area */}
        <div className="flex-1 overflow-hidden">
          <TabsContent value="chats" className="mt-0 h-full">
            <ChatTab />
          </TabsContent>
          
          <TabsContent value="transactions" className="mt-0 h-full">
            <TransactionsTab />
          </TabsContent>
          
          <TabsContent value="reports" className="mt-0 h-full">
            <ReportsTab />
          </TabsContent>
          
          <TabsContent value="team" className="mt-0 h-full">
            <TeamTab />
          </TabsContent>
        </div>

        {/* Bottom navigation bar */}
        <TabsList className="w-full grid grid-cols-4 h-16 rounded-none border-t bg-background sticky bottom-0">
          <TabsTrigger value="chats" className="flex flex-col items-center gap-1 py-2">
            <MessageCircle className="w-5 h-5" />
            <span className="text-xs">Chat</span>
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex flex-col items-center gap-1 py-2">
            <List className="w-5 h-5" />
            <span className="text-xs">Transaksi</span>
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex flex-col items-center gap-1 py-2">
            <FileText className="w-5 h-5" />
            <span className="text-xs">Laporan</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex flex-col items-center gap-1 py-2">
            <Users className="w-5 h-5" />
            <span className="text-xs">Tim</span>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};
