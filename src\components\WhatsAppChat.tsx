import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { RefreshCw, Phone, ArrowLeft, Send, Paperclip } from 'lucide-react';
import { useProfile } from '@/hooks/useProfile';
import { wahaService, WhatsAppMessage } from '@/services/wahaService';
import { toast } from 'sonner';

interface WhatsAppChatProps {
  onBack: () => void;
}

export const WhatsAppChat: React.FC<WhatsAppChatProps> = ({ onBack }) => {
  const { profile, updatePhoneNumber } = useProfile();
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isSettingPhone, setIsSettingPhone] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (profile?.phone_number) {
      setPhoneNumber(profile.phone_number);
      fetchMessages();
    } else {
      setIsSettingPhone(true);
    }
  }, [profile]);

  const fetchMessages = async () => {
    if (!phoneNumber || sendingMessage) return;
    
    setLoading(true);
    try {
      const fetchedMessages = await wahaService.fetchMessages(phoneNumber);
      // Sort messages by timestamp ascending (oldest first, newest last)
      const sortedMessages = fetchedMessages.sort((a, b) => a.timestamp - b.timestamp);
      setMessages(sortedMessages);
    } catch (error) {
      toast.error('Gagal memuat pesan WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneNumberSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber.trim()) {
      toast.error('Nomor telepon harus diisi');
      return;
    }

    const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');
    const success = await updatePhoneNumber(cleanPhoneNumber);
    
    if (success) {
      setIsSettingPhone(false);
      toast.success('Nomor telepon berhasil disimpan');
      fetchMessages();
    } else {
      toast.error('Gagal menyimpan nomor telepon');
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('id-ID');
  };

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sendingMessage) return;
    
    const messageToSend = messageInput;
    setSendingMessage(true);
    
    try {
      console.log('Sending message:', messageToSend);
      
      const response = await fetch('https://wabot-waha.libslm.easypanel.host/api/sendText', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId: "<EMAIL>",
          reply_to: "6281220100582",
          text: messageToSend,
          linkPreview: true,
          linkPreviewHighQuality: false,
          session: "kresna"
        }),
      });

      console.log('Send response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('Send response data:', responseData);

      // Clear input immediately after successful send
      setMessageInput('');
      toast.success('Pesan terkirim');
      
      // Wait a bit longer before refreshing to ensure message is processed
      setTimeout(() => {
        fetchMessages();
      }, 2000);
      
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Gagal mengirim pesan');
    } finally {
      setSendingMessage(false);
    }
  };

  const handleFileAttach = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      toast.success(`${files.length} file dipilih`);
      // In a real implementation, you would handle file upload here
    }
  };

  if (isSettingPhone) {
    return (
      <div className="flex flex-col h-[calc(100vh-60px)]">
        <div className="flex items-center gap-3 p-4 border-b bg-background">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h3 className="font-semibold">Setup Nomor WhatsApp</h3>
        </div>

        <div className="flex-1 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <form onSubmit={handlePhoneNumberSubmit} className="space-y-4">
                <div className="text-center mb-4">
                  <Phone className="w-12 h-12 mx-auto mb-2 text-green-600" />
                  <h3 className="text-lg font-semibold">Hubungkan WhatsApp</h3>
                  <p className="text-sm text-muted-foreground">
                    Masukkan nomor WhatsApp Anda untuk melihat chat
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Nomor WhatsApp</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="6287784130824"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Format: 62xxxxxxxxxx (tanpa tanda + atau spasi)
                  </p>
                </div>
                
                <Button type="submit" className="w-full">
                  Simpan & Lanjutkan
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-green-500">
            WA
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <h3 className="font-semibold">WhatsApp Chat</h3>
          <p className="text-sm text-muted-foreground">+{phoneNumber}</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchMessages}
          disabled={loading || sendingMessage}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {loading ? (
          <div className="text-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Memuat pesan...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground">Belum ada pesan</p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${!message.fromMe ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                    !message.fromMe
                      ? 'bg-green-500 text-white'
                      : 'bg-muted text-foreground'
                  }`}
                >
                  <p className="text-sm">{message.body}</p>
                  <span className={`text-xs block mt-1 ${
                    !message.fromMe ? 'text-green-100' : 'text-muted-foreground'
                  }`}>
                    {formatTimestamp(message.timestamp)}
                  </span>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="p-4 border-t bg-background">
        <div className="flex gap-2 items-center">
          <Button 
            variant="outline" 
            size="icon"
            onClick={handleFileAttach}
            className="shrink-0"
            disabled={sendingMessage}
          >
            <Paperclip className="w-4 h-4" />
          </Button>
          <Input
            placeholder="Ketik pesan..."
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            className="flex-1"
            disabled={sendingMessage}
          />
          <Button 
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || sendingMessage}
            className="shrink-0"
          >
            <Send className="w-4 h-4 mr-2" />
            {sendingMessage ? 'Mengirim...' : 'Kirim'}
          </Button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileChange}
          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt"
        />
      </div>
    </div>
  );
};
