import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Send, RefreshCw, Download, Eye, Users, User, Phone, Mail, MapPin, Plus, Search } from 'lucide-react';
import { useBigCapital } from '@/hooks/useBigCapital';
import { BigCapitalInvoice } from '@/services/bigCapitalService';
import { toast } from 'sonner';

interface CustomerAIChatProps {
  onBack: () => void;
}

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  invoices?: BigCapitalInvoice[];
  customers?: Customer[];
}

interface Customer {
  id: string;
  display_name: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  currency_code?: string;
  balance?: number;
  created_at?: string;
  updated_at?: string;
}

export const CustomerAIChat: React.FC<CustomerAIChatProps> = ({ onBack }) => {
  const [activeTab, setActiveTab] = useState('chat');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const { loading, invoices, fetchInvoices, testAuthentication } = useBigCapital();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    setMessages([{
      id: '1',
      text: 'Halo! Saya AI Pelanggan. Saya dapat membantu Anda mengelola invoice dan pembayaran pelanggan dari BigCapital. Ketik "invoice" untuk melihat daftar invoice, "customer" untuk melihat daftar pelanggan, atau gunakan tab Contact List untuk mengelola kontak pelanggan.',
      isUser: false,
      timestamp: new Date(),
    }]);
    
    // Load customers on component mount
    fetchCustomers();
  }, []);

  useEffect(() => {
    // Filter customers based on search term
    const filtered = customers.filter(customer =>
      customer.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.includes(searchTerm)
    );
    setFilteredCustomers(filtered);
  }, [customers, searchTerm]);

  const fetchCustomers = async () => {
    try {
      // Mock customer data - replace with actual API call
      const mockCustomers: Customer[] = [
        {
          id: '1',
          display_name: 'PT. Klien Utama',
          email: '<EMAIL>',
          phone: '+62-21-1111222',
          website: 'www.klienutama.com',
          address: 'Jl. Thamrin No. 100',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: 25000000,
          created_at: '2024-01-10T08:00:00Z'
        },
        {
          id: '2',
          display_name: 'CV. Mitra Bisnis',
          email: '<EMAIL>',
          phone: '+62-21-3333444',
          address: 'Jl. Kuningan No. 200',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: 15500000,
          created_at: '2024-02-15T10:30:00Z'
        },
        {
          id: '3',
          display_name: 'Toko Modern Sejahtera',
          email: '<EMAIL>',
          phone: '+62-21-7777888',
          address: 'Jl. Kemang No. 300',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: 8200000,
          created_at: '2024-03-05T14:15:00Z'
        }
      ];
      
      setCustomers(mockCustomers);
      toast.success('Data pelanggan berhasil dimuat');
    } catch (error) {
      console.error('Error fetching customers:', error);
      toast.error('Gagal memuat data pelanggan');
    }
  };

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sendingMessage) return;
    
    const userMessage = messageInput.trim().toLowerCase();
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageInput,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setMessageInput('');
    setSendingMessage(true);

    try {
      let aiResponse: ChatMessage;

      if (userMessage.includes('invoice') || userMessage.includes('tagihan')) {
        // Fetch invoices from BigCapital
        await fetchInvoices();
        
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Berikut adalah daftar invoice dari BigCapital:`,
          isUser: false,
          timestamp: new Date(),
          invoices: invoices,
        };
      } else if (userMessage.includes('customer') || userMessage.includes('pelanggan') || userMessage.includes('klien')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Berikut adalah daftar pelanggan Anda (${customers.length} pelanggan):`,
          isUser: false,
          timestamp: new Date(),
          customers: customers,
        };
      } else if (userMessage.includes('auth') || userMessage.includes('login') || userMessage.includes('koneksi')) {
        // Test authentication
        const success = await testAuthentication();
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: success 
            ? 'Koneksi ke BigCapital berhasil! Sekarang saya dapat mengakses data invoice Anda.'
            : 'Gagal mengkoneksikan ke BigCapital. Silakan periksa kredensial Anda.',
          isUser: false,
          timestamp: new Date(),
        };
      } else if (userMessage.includes('help') || userMessage.includes('bantuan')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Saya dapat membantu Anda dengan:\n\n• Melihat daftar invoice (ketik "invoice")\n• Melihat daftar pelanggan (ketik "customer")\n• Mengecek koneksi API (ketik "auth")\n• Mengelola kontak pelanggan (gunakan tab Contact List)\n• Informasi pembayaran pelanggan\n• Status tagihan\n\nApa yang ingin Anda lakukan?',
          isUser: false,
          timestamp: new Date(),
        };
      } else {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Maaf, saya belum memahami permintaan Anda. Silakan ketik "help" untuk melihat apa yang bisa saya bantu, atau ketik "invoice" untuk melihat daftar tagihan.',
          isUser: false,
          timestamp: new Date(),
        };
      }

      setTimeout(() => {
        setMessages(prev => [...prev, aiResponse]);
      }, 500);

    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Maaf, terjadi kesalahan saat memproses permintaan Anda.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600'; // Customer owes money
    if (balance < 0) return 'text-red-600'; // We owe money to customer
    return 'text-gray-600'; // No balance
  };

  const getBalanceText = (balance: number) => {
    if (balance > 0) return `Piutang: ${formatCurrency(balance)}`;
    if (balance < 0) return `Hutang: ${formatCurrency(Math.abs(balance))}`;
    return 'Lunas';
  };

  return (
    <div className="flex flex-col h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-blue-500">
            AP
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <h3 className="font-semibold">AI Pelanggan</h3>
          <p className="text-sm text-muted-foreground">Customer Management Assistant</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchInvoices}
          disabled={loading}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <Send className="w-4 h-4" />
            Chat AI
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Contact List
          </TabsTrigger>
        </TabsList>

        {/* Chat Tab */}
        <TabsContent value="chat" className="flex-1 flex flex-col mt-0">
          {/* Messages */}
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {messages.map((message) => (
              <div key={message.id} className="space-y-2">
                <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                    message.isUser ? 'bg-blue-500 text-white' : 'bg-muted text-foreground'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                    <span className={`text-xs block mt-1 ${
                      message.isUser ? 'text-blue-100' : 'text-muted-foreground'
                    }`}>
                      {message.timestamp.toLocaleTimeString('id-ID')}
                    </span>
                  </div>
                </div>

                {/* Customer Cards */}
                {message.customers && message.customers.length > 0 && (
                  <div className="space-y-2 ml-4">
                    {message.customers.slice(0, 3).map((customer) => (
                      <Card key={customer.id} className="text-sm">
                        <CardContent className="p-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold">{customer.display_name}</h4>
                              <p className="text-xs text-muted-foreground">{customer.email}</p>
                              <p className="text-xs text-muted-foreground">{customer.phone}</p>
                              <p className={`text-xs font-medium ${getBalanceColor(customer.balance || 0)}`}>
                                {getBalanceText(customer.balance || 0)}
                              </p>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {customer.currency_code}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {message.customers.length > 3 && (
                      <p className="text-xs text-muted-foreground ml-2">
                        +{message.customers.length - 3} pelanggan lainnya
                      </p>
                    )}
                  </div>
                )}
              </div>
            ))}

            {sendingMessage && (
              <div className="flex justify-start">
                <div className="bg-muted px-3 py-2 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t bg-background">
            <div className="flex gap-2 items-center">
              <Input
                placeholder="Ketik pesan Anda..."
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                className="flex-1"
                disabled={sendingMessage}
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || sendingMessage}
                className="shrink-0"
              >
                <Send className="w-4 h-4 mr-2" />
                {sendingMessage ? 'Mengirim...' : 'Kirim'}
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Contact List Tab */}
        <TabsContent value="contacts" className="flex-1 flex flex-col mt-0">
          <div className="p-4 space-y-4 flex-1 overflow-y-auto">
            {/* Search and Add */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Cari pelanggan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={() => setShowAddCustomer(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah
              </Button>
            </div>

            {/* Customer Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-blue-600">{customers.length}</h4>
                  <p className="text-sm text-muted-foreground">Total Pelanggan</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-green-600">
                    {customers.filter(c => (c.balance || 0) > 0).length}
                  </h4>
                  <p className="text-sm text-muted-foreground">Pelanggan Piutang</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-green-600">
                    {formatCurrency(customers.reduce((sum, c) => sum + Math.abs(c.balance || 0), 0))}
                  </h4>
                  <p className="text-sm text-muted-foreground">Total Saldo</p>
                </div>
              </Card>
            </div>

            {/* Customer List */}
            <div className="space-y-3">
              {filteredCustomers.length === 0 ? (
                <div className="text-center py-8">
                  <User className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Tidak ada pelanggan ditemukan</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? 'Coba kata kunci lain' : 'Belum ada pelanggan yang terdaftar'}
                  </p>
                  <Button onClick={() => setShowAddCustomer(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Tambah Pelanggan Pertama
                  </Button>
                </div>
              ) : (
                filteredCustomers.map((customer) => (
                  <Card key={customer.id} className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => setSelectedCustomer(customer)}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Avatar className="w-12 h-12">
                          <AvatarFallback className="bg-blue-100 text-blue-700">
                            {customer.display_name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold truncate">{customer.display_name}</h3>
                          <div className="space-y-1 text-sm text-muted-foreground">
                            {customer.email && (
                              <div className="flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                <span className="truncate">{customer.email}</span>
                              </div>
                            )}
                            {customer.phone && (
                              <div className="flex items-center gap-1">
                                <Phone className="w-3 h-3" />
                                <span>{customer.phone}</span>
                              </div>
                            )}
                            {customer.address && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span className="truncate">{customer.address}, {customer.city}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getBalanceColor(customer.balance || 0)}`}>
                          {getBalanceText(customer.balance || 0)}
                        </p>
                        <Badge variant="outline" className="mt-1">
                          {customer.currency_code}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
