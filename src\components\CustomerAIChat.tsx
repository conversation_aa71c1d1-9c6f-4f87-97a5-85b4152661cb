import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ArrowLeft, Send, RefreshCw, Download, Eye } from 'lucide-react';
import { useBigCapital } from '@/hooks/useBigCapital';
import { BigCapitalInvoice } from '@/services/bigCapitalService';
import { toast } from 'sonner';

interface CustomerAIChatProps {
  onBack: () => void;
}

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  invoices?: BigCapitalInvoice[];
}

export const CustomerAIChat: React.FC<CustomerAIChatProps> = ({ onBack }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const { loading, invoices, fetchInvoices, testAuthentication } = useBigCapital();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    setMessages([{
      id: '1',
      text: 'Halo! Saya AI Pelanggan. Saya dapat membantu Anda mengelola invoice dan pembayaran pelanggan dari BigCapital. Ketik "invoice" untuk melihat daftar invoice, atau tanyakan apa yang Anda butuhkan.',
      isUser: false,
      timestamp: new Date(),
    }]);
  }, []);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sendingMessage) return;
    
    const userMessage = messageInput.trim().toLowerCase();
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageInput,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setMessageInput('');
    setSendingMessage(true);

    try {
      // Process the message and generate AI response
      let aiResponse: ChatMessage;

      if (userMessage.includes('invoice') || userMessage.includes('tagihan')) {
        // Fetch invoices from BigCapital
        await fetchInvoices();
        
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Berikut adalah daftar invoice dari BigCapital:`,
          isUser: false,
          timestamp: new Date(),
          invoices: invoices,
        };
      } else if (userMessage.includes('auth') || userMessage.includes('login') || userMessage.includes('koneksi')) {
        // Test authentication
        const success = await testAuthentication();
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: success 
            ? 'Koneksi ke BigCapital berhasil! Sekarang saya dapat mengakses data invoice Anda.'
            : 'Gagal mengkoneksikan ke BigCapital. Silakan periksa kredensial Anda.',
          isUser: false,
          timestamp: new Date(),
        };
      } else if (userMessage.includes('help') || userMessage.includes('bantuan')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Saya dapat membantu Anda dengan:\n\n• Melihat daftar invoice (ketik "invoice")\n• Mengecek koneksi API (ketik "auth")\n• Informasi pembayaran pelanggan\n• Status tagihan\n\nApa yang ingin Anda lakukan?',
          isUser: false,
          timestamp: new Date(),
        };
      } else {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Maaf, saya belum memahami permintaan Anda. Silakan ketik "help" untuk melihat apa yang bisa saya bantu, atau ketik "invoice" untuk melihat daftar tagihan.',
          isUser: false,
          timestamp: new Date(),
        };
      }

      setTimeout(() => {
        setMessages(prev => [...prev, aiResponse]);
      }, 500);

    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Maaf, terjadi kesalahan saat memproses permintaan Anda.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  return (
    <div className="flex flex-col h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-blue-500">
            AP
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <h3 className="font-semibold">AI Pelanggan</h3>
          <p className="text-sm text-muted-foreground">BigCapital Invoice Assistant</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchInvoices}
          disabled={loading}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {messages.map((message) => (
          <div key={message.id} className="space-y-2">
            <div
              className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                  message.isUser
                    ? 'bg-blue-500 text-white'
                    : 'bg-muted text-foreground'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                <span className={`text-xs block mt-1 ${
                  message.isUser ? 'text-blue-100' : 'text-muted-foreground'
                }`}>
                  {message.timestamp.toLocaleTimeString('id-ID')}
                </span>
              </div>
            </div>

            {/* Invoice Cards */}
            {message.invoices && message.invoices.length > 0 && (
              <div className="space-y-2 ml-4">
                {message.invoices.slice(0, 5).map((invoice) => (
                  <Card key={invoice.id} className="text-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex justify-between items-center">
                        <span>#{invoice.invoice_number}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                          invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {invoice.status}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-1">
                        <p><strong>Pelanggan:</strong> {invoice.customer_name}</p>
                        <p><strong>Jumlah:</strong> {formatCurrency(invoice.amount)}</p>
                        <p><strong>Jatuh Tempo:</strong> {formatDate(invoice.due_date)}</p>
                      </div>
                      <div className="flex gap-2 mt-2">
                        <Button size="sm" variant="outline">
                          <Eye className="w-3 h-3 mr-1" />
                          Lihat
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-3 h-3 mr-1" />
                          Unduh
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                {message.invoices.length > 5 && (
                  <p className="text-xs text-muted-foreground ml-2">
                    +{message.invoices.length - 5} invoice lainnya
                  </p>
                )}
              </div>
            )}
          </div>
        ))}

        {sendingMessage && (
          <div className="flex justify-start">
            <div className="bg-muted px-3 py-2 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t bg-background">
        <div className="flex gap-2 items-center">
          <Input
            placeholder="Ketik pesan Anda..."
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            className="flex-1"
            disabled={sendingMessage}
          />
          <Button 
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || sendingMessage}
            className="shrink-0"
          >
            <Send className="w-4 h-4 mr-2" />
            {sendingMessage ? 'Mengirim...' : 'Kirim'}
          </Button>
        </div>
      </div>
    </div>
  );
};
