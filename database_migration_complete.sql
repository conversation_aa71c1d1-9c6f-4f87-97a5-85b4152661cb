-- =====================================================
-- COMPLETE DATABASE MIGRATION FOR REPORT SETTINGS
-- =====================================================
-- Project: transync (pmcountrcoqanobyewlq)
-- Date: 2025-06-19
-- Description: Complete migration for report settings functionality

-- =====================================================
-- 1. CREATE REPORT SETTINGS TABLE
-- =====================================================

-- Drop table if exists (for clean migration)
DROP TABLE IF EXISTS public.report_settings CASCADE;

-- Create report_settings table with all necessary columns
CREATE TABLE public.report_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    report_type TEXT NOT NULL CHECK (report_type IN (
        'profit-loss', 
        'balance-sheet', 
        'cash-flow', 
        'transaction-summary', 
        'petty-cash', 
        'vendor-summary', 
        'customer-summary'
    )),
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    company_name TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_date_range CHECK (from_date <= to_date),
    CONSTRAINT unique_user_report_type UNIQUE(user_id, report_type)
);

-- Add table comment
COMMENT ON TABLE public.report_settings IS 'Stores user-specific report settings and preferences';

-- Add column comments
COMMENT ON COLUMN public.report_settings.report_type IS 'Type of financial report';
COMMENT ON COLUMN public.report_settings.settings IS 'Additional JSON settings for the report';
COMMENT ON COLUMN public.report_settings.from_date IS 'Report start date';
COMMENT ON COLUMN public.report_settings.to_date IS 'Report end date';

-- =====================================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary indexes for common queries
CREATE INDEX idx_report_settings_user_id ON public.report_settings(user_id);
CREATE INDEX idx_report_settings_report_type ON public.report_settings(report_type);
CREATE INDEX idx_report_settings_user_report_type ON public.report_settings(user_id, report_type);
CREATE INDEX idx_report_settings_created_at ON public.report_settings(created_at DESC);
CREATE INDEX idx_report_settings_updated_at ON public.report_settings(updated_at DESC);

-- Partial indexes for active reports
CREATE INDEX idx_report_settings_recent ON public.report_settings(user_id, updated_at DESC) 
WHERE updated_at > NOW() - INTERVAL '30 days';

-- =====================================================
-- 3. ENABLE ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on the table
ALTER TABLE public.report_settings ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies
-- Policy 1: Users can view their own report settings
CREATE POLICY "Users can view own report settings" 
ON public.report_settings FOR SELECT 
USING (auth.uid() = user_id);

-- Policy 2: Users can insert their own report settings
CREATE POLICY "Users can insert own report settings" 
ON public.report_settings FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Policy 3: Users can update their own report settings
CREATE POLICY "Users can update own report settings" 
ON public.report_settings FOR UPDATE 
USING (auth.uid() = user_id) 
WITH CHECK (auth.uid() = user_id);

-- Policy 4: Users can delete their own report settings
CREATE POLICY "Users can delete own report settings" 
ON public.report_settings FOR DELETE 
USING (auth.uid() = user_id);

-- =====================================================
-- 4. CREATE TRIGGER FUNCTIONS
-- =====================================================

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto-updating timestamps
DROP TRIGGER IF EXISTS trigger_report_settings_updated_at ON public.report_settings;
CREATE TRIGGER trigger_report_settings_updated_at
    BEFORE UPDATE ON public.report_settings
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- 5. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to save/update report settings (UPSERT)
CREATE OR REPLACE FUNCTION public.save_report_settings(
    p_report_type TEXT,
    p_from_date DATE,
    p_to_date DATE,
    p_company_name TEXT DEFAULT NULL,
    p_settings JSONB DEFAULT '{}'
)
RETURNS public.report_settings AS $$
DECLARE
    result_record public.report_settings;
BEGIN
    -- Validate inputs
    IF p_from_date > p_to_date THEN
        RAISE EXCEPTION 'from_date cannot be greater than to_date';
    END IF;
    
    -- Upsert the record
    INSERT INTO public.report_settings (
        user_id,
        report_type,
        from_date,
        to_date,
        company_name,
        settings
    ) VALUES (
        auth.uid(),
        p_report_type,
        p_from_date,
        p_to_date,
        p_company_name,
        p_settings
    )
    ON CONFLICT (user_id, report_type)
    DO UPDATE SET
        from_date = EXCLUDED.from_date,
        to_date = EXCLUDED.to_date,
        company_name = EXCLUDED.company_name,
        settings = EXCLUDED.settings,
        updated_at = NOW()
    RETURNING * INTO result_record;
    
    RETURN result_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's report settings
CREATE OR REPLACE FUNCTION public.get_user_report_settings(
    p_report_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    report_type TEXT,
    from_date DATE,
    to_date DATE,
    company_name TEXT,
    settings JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    IF p_report_type IS NULL THEN
        -- Return all report settings for the user
        RETURN QUERY
        SELECT 
            rs.id,
            rs.report_type,
            rs.from_date,
            rs.to_date,
            rs.company_name,
            rs.settings,
            rs.created_at,
            rs.updated_at
        FROM public.report_settings rs
        WHERE rs.user_id = auth.uid()
        ORDER BY rs.updated_at DESC;
    ELSE
        -- Return specific report type settings
        RETURN QUERY
        SELECT 
            rs.id,
            rs.report_type,
            rs.from_date,
            rs.to_date,
            rs.company_name,
            rs.settings,
            rs.created_at,
            rs.updated_at
        FROM public.report_settings rs
        WHERE rs.user_id = auth.uid() 
        AND rs.report_type = p_report_type
        ORDER BY rs.updated_at DESC
        LIMIT 1;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get latest settings for each report type
CREATE OR REPLACE FUNCTION public.get_latest_report_settings()
RETURNS TABLE (
    report_type TEXT,
    from_date DATE,
    to_date DATE,
    company_name TEXT,
    settings JSONB,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (rs.report_type)
        rs.report_type,
        rs.from_date,
        rs.to_date,
        rs.company_name,
        rs.settings,
        rs.updated_at as last_updated
    FROM public.report_settings rs
    WHERE rs.user_id = auth.uid()
    ORDER BY rs.report_type, rs.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete old report settings (cleanup)
CREATE OR REPLACE FUNCTION public.cleanup_old_report_settings(
    p_days_old INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.report_settings
    WHERE user_id = auth.uid()
    AND updated_at < NOW() - (p_days_old || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant table permissions
GRANT ALL ON public.report_settings TO authenticated;
GRANT ALL ON public.report_settings TO service_role;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION public.save_report_settings(TEXT, DATE, DATE, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_report_settings(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_latest_report_settings() TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_old_report_settings(INTEGER) TO authenticated;

-- =====================================================
-- 7. INSERT SAMPLE DATA (OPTIONAL)
-- =====================================================

-- Uncomment below to insert sample data for testing
/*
-- Sample data for testing (requires authenticated user)
INSERT INTO public.report_settings (user_id, report_type, from_date, to_date, company_name, settings)
SELECT 
    auth.uid(),
    'balance-sheet',
    '2025-06-01'::DATE,
    '2025-06-30'::DATE,
    'PT. Contoh Perusahaan',
    '{"currency": "IDR", "format": "standard"}'::JSONB
WHERE auth.uid() IS NOT NULL;

INSERT INTO public.report_settings (user_id, report_type, from_date, to_date, company_name, settings)
SELECT 
    auth.uid(),
    'profit-loss',
    '2025-06-01'::DATE,
    '2025-06-30'::DATE,
    'PT. Contoh Perusahaan',
    '{"currency": "IDR", "format": "detailed"}'::JSONB
WHERE auth.uid() IS NOT NULL;
*/

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Verify table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'report_settings'
ORDER BY ordinal_position;

-- Verify indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'report_settings' 
AND schemaname = 'public';

-- Verify RLS policies
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'report_settings' 
AND schemaname = 'public';

-- Verify functions
SELECT 
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%report_settings%'
ORDER BY routine_name;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

SELECT 'Database migration completed successfully!' as status,
       NOW() as completed_at;
