
-- Create a table to store BigCapital API credentials and tokens
CREATE TABLE public.bigcapital_auth (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  credential_email TEXT NOT NULL,
  credential_password TEXT NOT NULL,
  access_token TEXT,
  organization_id TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Add Row Level Security (RLS)
ALTER TABLE public.bigcapital_auth ENABLE ROW LEVEL SECURITY;

-- Create policies for the bigcapital_auth table
CREATE POLICY "Users can view their own auth data" 
  ON public.bigcapital_auth 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own auth data" 
  ON public.bigcapital_auth 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own auth data" 
  ON public.bigcapital_auth 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own auth data" 
  ON public.bigcapital_auth 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Insert default credentials for the admin user
-- Note: This will only work if a user with this email exists in auth.users
INSERT INTO public.bigcapital_auth (user_id, credential_email, credential_password)
SELECT 
  id,
  '<EMAIL>',
  '12345678'
FROM auth.users 
WHERE email = '<EMAIL>'
LIMIT 1;

-- If the above doesn't work (user doesn't exist), you can manually insert with your actual user ID
-- Replace 'your-actual-user-id' with your real user ID from the auth system
-- INSERT INTO public.bigcapital_auth (user_id, credential_email, credential_password)
-- VALUES ('your-actual-user-id', '<EMAIL>', '12345678');
