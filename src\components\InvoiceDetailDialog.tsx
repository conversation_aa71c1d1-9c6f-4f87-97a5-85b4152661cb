
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Updated interface to match the API response format
interface Invoice {
  id: number;
  invoice_no: string;
  customer_id: number;
  customer: {
    id: number;
    display_name: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  invoice_date: string;
  invoice_date_formatted: string;
  due_date: string;
  due_date_formatted: string;
  total: number;
  total_formatted: string;
  balance: number;
  due_amount: number;
  due_amount_formatted: string;
  payment_amount: number;
  payment_amount_formatted: string;
  currency_code: string;
  created_at: string;
  created_at_formatted: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  invoice_message: string;
  terms_conditions: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string;
    };
  }>;
}

interface InvoiceDetailDialogProps {
  invoice: Invoice | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const InvoiceDetailDialog: React.FC<InvoiceDetailDialogProps> = ({
  invoice,
  open,
  onOpenChange,
}) => {
  if (!invoice) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = () => {
    if (invoice.is_fully_paid) return 'bg-green-100 text-green-800';
    if (invoice.is_overdue) return 'bg-red-100 text-red-800';
    if (invoice.is_partially_paid) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getStatusText = () => {
    if (invoice.is_fully_paid) return 'Paid';
    if (invoice.is_overdue) return 'Overdue';
    if (invoice.is_partially_paid) return 'Partially Paid';
    return 'Pending';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Detail Invoice {invoice.invoice_no}</span>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Invoice</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Nomor Invoice:</span>
                  <p className="font-semibold">{invoice.invoice_no}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Pelanggan:</span>
                  <p className="font-semibold">{invoice.customer.display_name}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Tanggal Dibuat:</span>
                  <p>{invoice.invoice_date_formatted}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Jatuh Tempo:</span>
                  <p>{invoice.due_date_formatted}</p>
                </div>
                {invoice.reference_no && (
                  <div>
                    <span className="text-sm text-muted-foreground">Nomor Referensi:</span>
                    <p>{invoice.reference_no}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ringkasan Pembayaran</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-sm text-muted-foreground">Total Amount:</span>
                  <p className="text-2xl font-bold text-green-600">
                    {invoice.total_formatted}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Jumlah Terbayar:</span>
                  <p className="font-semibold">{invoice.payment_amount_formatted}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Sisa Tagihan:</span>
                  <p className="font-semibold">{invoice.due_amount_formatted}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <p className="font-semibold">{getStatusText()}</p>
                </div>
                {invoice.is_overdue && (
                  <div>
                    <span className="text-sm text-muted-foreground">Overdue:</span>
                    <p className="font-semibold text-red-600">{invoice.overdue_days} hari</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Invoice Items */}
          {invoice.entries && invoice.entries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Item Invoice</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Deskripsi</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Rate</TableHead>
                      <TableHead>Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoice.entries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">
                          {entry.item.name}
                        </TableCell>
                        <TableCell>
                          {entry.description || '-'}
                        </TableCell>
                        <TableCell>
                          {entry.quantity}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(entry.rate)}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(entry.total)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Additional Information */}
          {(invoice.invoice_message || invoice.terms_conditions) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {invoice.invoice_message && (
                  <div>
                    <span className="text-sm text-muted-foreground">Pesan Invoice:</span>
                    <p>{invoice.invoice_message}</p>
                  </div>
                )}
                {invoice.terms_conditions && (
                  <div>
                    <span className="text-sm text-muted-foreground">Syarat & Ketentuan:</span>
                    <p>{invoice.terms_conditions}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
