# Database Migration Guide - Report Settings

## ✅ Migration Status: COMPLETED

Database migration has been successfully implemented in Supabase project **"transync"** (ID: pmcountrcoqanobyewlq).

## 📋 Migration Summary

### 1. **Table Created: `report_settings`**
```sql
CREATE TABLE public.report_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    report_type TEXT NOT NULL,
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    company_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, report_type)
);
```

### 2. **Indexes Created**
- `report_settings_user_id_idx` - For user-based queries
- `report_settings_report_type_idx` - For report type filtering
- `report_settings_user_report_type_idx` - For combined user+type queries
- `report_settings_created_at_idx` - For date-based sorting

### 3. **Row Level Security (RLS)**
- ✅ RLS Enabled
- ✅ 4 Policies Created:
  - Users can view own report settings
  - Users can insert own report settings
  - Users can update own report settings
  - Users can delete own report settings

### 4. **Helper Functions Created**

#### `get_user_report_settings(p_report_type TEXT DEFAULT NULL)`
- Returns user's report settings
- Optional filter by report type
- Ordered by updated_at DESC

#### `save_report_settings(p_report_type, p_from_date, p_to_date, p_company_name)`
- Upsert functionality (insert or update)
- Automatic conflict resolution
- Returns the saved record

#### `get_latest_report_settings()`
- Returns latest settings for each report type
- Useful for dashboard overview

#### `handle_report_settings_updated()`
- Trigger function for auto-updating timestamps
- Automatically called on UPDATE operations

### 5. **Triggers**
- `on_report_settings_updated` - Auto-updates `updated_at` field

## 🔧 Configuration Setup

### 1. **Environment Variables**
Copy `.env.example` to `.env` and configure:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://pmcountrcoqanobyewlq.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

### 2. **Get Supabase Keys**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select project "transync"
3. Go to Settings → API
4. Copy the following:
   - Project URL: `https://pmcountrcoqanobyewlq.supabase.co`
   - Anon/Public Key: `eyJ...` (starts with eyJ)

## 🧪 Testing the Migration

### 1. **Test Database Connection**
```javascript
import { supabase } from '@/lib/supabase';

// Test connection
const { data, error } = await supabase
  .from('report_settings')
  .select('count(*)')
  .single();

console.log('Connection test:', { data, error });
```

### 2. **Test Save Function**
```javascript
// Test saving report settings
const { data, error } = await supabase.rpc('save_report_settings', {
  p_report_type: 'balance-sheet',
  p_from_date: '2025-06-01',
  p_to_date: '2025-06-30',
  p_company_name: 'Test Company'
});

console.log('Save test:', { data, error });
```

### 3. **Test Load Function**
```javascript
// Test loading report settings
const { data, error } = await supabase.rpc('get_user_report_settings', {
  p_report_type: 'balance-sheet'
});

console.log('Load test:', { data, error });
```

## 📊 Database Schema Verification

### Tables in Database:
- ✅ `bigcapital_auth`
- ✅ `billing_history`
- ✅ `billing_plans`
- ✅ `profiles`
- ✅ `team_members`
- ✅ `teams`
- ✅ `transaction_usage`
- ✅ **`report_settings`** (NEW)

### Functions Created:
- ✅ `get_user_report_settings(TEXT)`
- ✅ `save_report_settings(TEXT, DATE, DATE, TEXT)`
- ✅ `get_latest_report_settings()`
- ✅ `handle_report_settings_updated()`

### Security:
- ✅ RLS Enabled
- ✅ 4 Security Policies Active
- ✅ User Isolation Enforced

## 🚀 Next Steps

1. **Configure Environment**:
   - Copy `.env.example` to `.env`
   - Add your Supabase keys

2. **Test Application**:
   - Start the application: `npm run dev`
   - Go to Reports tab
   - Try saving report settings
   - Check if data persists

3. **Monitor Logs**:
   - Check browser console for Supabase operations
   - Verify data in Supabase dashboard

## 🔍 Troubleshooting

### Common Issues:

1. **Authentication Error**:
   - Ensure user is logged in
   - Check `auth.uid()` returns valid UUID

2. **RLS Policy Error**:
   - Verify user has proper authentication
   - Check if policies are correctly applied

3. **Function Not Found**:
   - Ensure all functions were created successfully
   - Check function permissions

### Debug Queries:

```sql
-- Check if table exists
SELECT * FROM information_schema.tables 
WHERE table_name = 'report_settings';

-- Check RLS policies
SELECT * FROM pg_policies 
WHERE tablename = 'report_settings';

-- Check functions
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%report_settings%';
```

## ✅ Migration Complete!

The database migration has been successfully completed. The application is now ready to use Supabase for storing and retrieving report settings with full security and user isolation.

**Project**: transync (pmcountrcoqanobyewlq)  
**Region**: ap-southeast-1  
**Status**: ACTIVE_HEALTHY  
**Database**: PostgreSQL 17.4.1.043
