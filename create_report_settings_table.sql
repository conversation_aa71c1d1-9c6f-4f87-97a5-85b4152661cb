-- Create Report Settings Table for Supabase
-- Run this SQL in your Supabase SQL Editor

-- 1. Create report_settings table
CREATE TABLE IF NOT EXISTS public.report_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    report_type TEXT NOT NULL,
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    company_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one setting per user per report type
    UNIQUE(user_id, report_type)
);

-- 2. Enable Row Level Security (RLS)
ALTER TABLE public.report_settings ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS policies for report_settings table
-- Policy: Users can view their own report settings
CREATE POLICY "Users can view own report settings" ON public.report_settings
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own report settings
CREATE POLICY "Users can insert own report settings" ON public.report_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own report settings
CREATE POLICY "Users can update own report settings" ON public.report_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own report settings
CREATE POLICY "Users can delete own report settings" ON public.report_settings
    FOR DELETE USING (auth.uid() = user_id);

-- 4. Create function to handle report settings updates
CREATE OR REPLACE FUNCTION public.handle_report_settings_updated()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger to automatically update updated_at timestamp
DROP TRIGGER IF EXISTS on_report_settings_updated ON public.report_settings;
CREATE TRIGGER on_report_settings_updated
    BEFORE UPDATE ON public.report_settings
    FOR EACH ROW EXECUTE FUNCTION public.handle_report_settings_updated();

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS report_settings_user_id_idx ON public.report_settings(user_id);
CREATE INDEX IF NOT EXISTS report_settings_report_type_idx ON public.report_settings(report_type);
CREATE INDEX IF NOT EXISTS report_settings_user_report_type_idx ON public.report_settings(user_id, report_type);
CREATE INDEX IF NOT EXISTS report_settings_created_at_idx ON public.report_settings(created_at);

-- 7. Grant necessary permissions
GRANT ALL ON public.report_settings TO authenticated;
GRANT ALL ON public.report_settings TO service_role;

-- 8. Create function to get user's report settings
CREATE OR REPLACE FUNCTION public.get_user_report_settings(
    p_report_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    report_type TEXT,
    from_date DATE,
    to_date DATE,
    company_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    IF p_report_type IS NULL THEN
        RETURN QUERY
        SELECT 
            rs.id,
            rs.report_type,
            rs.from_date,
            rs.to_date,
            rs.company_name,
            rs.created_at,
            rs.updated_at
        FROM public.report_settings rs
        WHERE rs.user_id = auth.uid()
        ORDER BY rs.updated_at DESC;
    ELSE
        RETURN QUERY
        SELECT 
            rs.id,
            rs.report_type,
            rs.from_date,
            rs.to_date,
            rs.company_name,
            rs.created_at,
            rs.updated_at
        FROM public.report_settings rs
        WHERE rs.user_id = auth.uid() AND rs.report_type = p_report_type
        ORDER BY rs.updated_at DESC
        LIMIT 1;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_user_report_settings(TEXT) TO authenticated;

-- 9. Create function to save/update report settings
CREATE OR REPLACE FUNCTION public.save_report_settings(
    p_report_type TEXT,
    p_from_date DATE,
    p_to_date DATE,
    p_company_name TEXT DEFAULT NULL
)
RETURNS public.report_settings AS $$
DECLARE
    result_record public.report_settings;
BEGIN
    INSERT INTO public.report_settings (
        user_id,
        report_type,
        from_date,
        to_date,
        company_name
    ) VALUES (
        auth.uid(),
        p_report_type,
        p_from_date,
        p_to_date,
        p_company_name
    )
    ON CONFLICT (user_id, report_type)
    DO UPDATE SET
        from_date = EXCLUDED.from_date,
        to_date = EXCLUDED.to_date,
        company_name = EXCLUDED.company_name,
        updated_at = NOW()
    RETURNING * INTO result_record;
    
    RETURN result_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.save_report_settings(TEXT, DATE, DATE, TEXT) TO authenticated;

-- 10. Create function to get latest report settings for each type
CREATE OR REPLACE FUNCTION public.get_latest_report_settings()
RETURNS TABLE (
    report_type TEXT,
    from_date DATE,
    to_date DATE,
    company_name TEXT,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (rs.report_type)
        rs.report_type,
        rs.from_date,
        rs.to_date,
        rs.company_name,
        rs.updated_at as last_updated
    FROM public.report_settings rs
    WHERE rs.user_id = auth.uid()
    ORDER BY rs.report_type, rs.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_latest_report_settings() TO authenticated;

-- 11. Verify the table was created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'report_settings'
ORDER BY ordinal_position;

-- 12. Show RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename = 'report_settings';

-- Success message
SELECT 'Report settings table created successfully!' as status;

-- 13. Insert sample data for testing (optional)
-- Uncomment the lines below if you want to insert sample data

/*
INSERT INTO public.report_settings (user_id, report_type, from_date, to_date, company_name)
SELECT 
    auth.uid(),
    'balance-sheet',
    '2025-06-01'::DATE,
    '2025-06-30'::DATE,
    'PT. Contoh Perusahaan'
WHERE auth.uid() IS NOT NULL;

INSERT INTO public.report_settings (user_id, report_type, from_date, to_date, company_name)
SELECT 
    auth.uid(),
    'profit-loss',
    '2025-06-01'::DATE,
    '2025-06-30'::DATE,
    'PT. Contoh Perusahaan'
WHERE auth.uid() IS NOT NULL;
*/
