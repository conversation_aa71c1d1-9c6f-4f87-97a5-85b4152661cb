import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, RefreshCw, CheckCircle, XCircle, Clock, Crown, Shield } from 'lucide-react';
import { DemoSwitcher } from '@/components/DemoSwitcher';
import { InvoiceDetailDialog } from '@/components/InvoiceDetailDialog';
import { VendorBillDetailDialog } from '@/components/VendorBillDetailDialog';
import { JournalDetailDialog } from '@/components/JournalDetailDialog';
import { DueDateCustomerDialog } from '@/components/DueDateCustomerDialog';
import { DueDateVendorDialog } from '@/components/DueDateVendorDialog';
import { CashFlowDialog } from '@/components/CashFlowDialog';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { bigCapitalService } from '@/services/bigCapitalService';

// Updated interface to match the new API response format
interface Invoice {
  id: number;
  invoice_no: string;
  customer_id: number;
  customer: {
    id: number;
    display_name: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  invoice_date: string;
  invoice_date_formatted: string;
  due_date: string;
  due_date_formatted: string;
  total: number;
  total_formatted: string;
  balance: number;
  due_amount: number;
  due_amount_formatted: string;
  payment_amount: number;
  payment_amount_formatted: string;
  currency_code: string;
  created_at: string;
  created_at_formatted: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  invoice_message: string;
  terms_conditions: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string;
    };
  }>;
}

// Interface for vendor bills from BigCapital API
interface VendorBill {
  id: number;
  bill_number: string | null;
  vendor_id: number;
  vendor: {
    id: number;
    contact_service: string;
    contact_type: string | null;
    balance: number;
    currency_code: string;
    first_name: string;
    last_name: string | null;
    company_name: string | null;
    display_name: string;
    email: string | null;
    work_phone: string | null;
    personal_phone: string | null;
    created_at: string;
    updated_at: string;
  };
  bill_date: string;
  formatted_bill_date: string;
  due_date: string;
  formatted_due_date: string;
  total: number;
  total_formatted: string;
  balance: number;
  formatted_balance: string;
  due_amount: number;
  formatted_due_amount: string;
  payment_amount: number;
  formatted_payment_amount: string;
  currency_code: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  note: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string | null;
    };
  }>;
}

// Interface for journal entries from API (updated to match actual response)
interface JournalEntry {
  id: number;
  journal_number: string;
  journal_type: string | null;
  date: string;
  formatted_date: string;
  reference: string | null;
  description: string | null;
  currency_code: string;
  amount: number;
  amount_formatted: string;
  formatted_amount: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  published_at: string;
  formatted_published_at: string;
  is_published: boolean;
  user_id: number;
  branch_id: number | null;
  attachment_file: string | null;
  exchange_rate: number;
  entries: Array<{
    id: number;
    account_id: number;
    contact_id: number | null;
    debit: number;
    credit: number;
    index: number;
    note: string | null;
    manual_journal_id: number;
    branch_id: number | null;
    project_id: number | null;
    account: {
      id: number;
      name: string;
      slug: string;
      code: string | null;
      account_type: string;
      account_type_label: string;
      account_parent_type: string;
      account_root_type: string;
      account_normal: string;
      account_normal_formatted: string;
      description: string | null;
      active: number;
      amount: number;
      currency_code: string;
      is_balance_sheet_account: boolean;
      is_pl_sheet: boolean;
      is_system_account: number;
    };
  }>;
}



// API service for fetching invoices from new endpoint
const fetchInvoices = async (accessToken?: string): Promise<Invoice[]> => {
  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authorization header if token is available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch('https://wabot-n8n.libslm.easypanel.host/webhook/transync/invoices/list', {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Handle different possible response structures
    if (Array.isArray(data)) {
      return data;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else if (data.invoices && Array.isArray(data.invoices)) {
      return data.invoices;
    } else {
      console.warn('Unexpected API response structure:', data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching invoices:', error);
    toast.error('Gagal memuat data invoice');
    throw error;
  }
};

// API service for fetching journal entries
const fetchJournalEntries = async (accessToken?: string): Promise<JournalEntry[]> => {
  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authorization header if token is available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch('https://wabot-n8n.libslm.easypanel.host/webhook/transync/journals/list', {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Handle different possible response structures
    if (Array.isArray(data)) {
      return data;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else if (data.journals && Array.isArray(data.journals)) {
      return data.journals;
    } else {
      console.warn('Unexpected journal API response structure:', data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching journal entries:', error);
    toast.error('Gagal memuat data jurnal umum');
    throw error;
  }
};

export const TransactionsTab = () => {
  const { session, user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('semua');
  const [aiFilter, setAiFilter] = useState('semua');

  // Demo state for role and plan management
  const [userRole, setUserRole] = useState<'admin' | 'general' | 'petty_cash' | 'staff'>('staff');
  const [userPlan, setUserPlan] = useState<'free' | 'premium' | 'enterprise'>('premium');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [selectedVendorBill, setSelectedVendorBill] = useState<VendorBill | null>(null);
  const [selectedJournalEntry, setSelectedJournalEntry] = useState<JournalEntry | null>(null);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [vendorBillDialogOpen, setVendorBillDialogOpen] = useState(false);
  const [journalDialogOpen, setJournalDialogOpen] = useState(false);
  const [dueDateCustomerDialogOpen, setDueDateCustomerDialogOpen] = useState(false);
  const [dueDateVendorDialogOpen, setDueDateVendorDialogOpen] = useState(false);
  const [cashFlowDialogOpen, setCashFlowDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [vendorBills, setVendorBills] = useState<VendorBill[]>([]);
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [customerCountdown, setCustomerCountdown] = useState<string>('');
  const [vendorCountdown, setVendorCountdown] = useState<string>('');

  // Function to fetch invoices
  const fetchInvoicesData = async () => {
    try {
      const accessToken = session?.access_token;
      console.log('Using access token for API request:', accessToken ? 'Token available' : 'No token');
      
      const fetchedInvoices = await fetchInvoices(accessToken);
      setInvoices(fetchedInvoices);
      toast.success(`Berhasil memuat ${fetchedInvoices.length} invoice`);
    } catch (err) {
      console.error('Error fetching invoices:', err);
      toast.error('Gagal memuat invoice');
      setInvoices([]);
    }
  };

  // Function to fetch vendor bills using BigCapital service
  const fetchVendorBillsData = async () => {
    if (!user) {
      console.log('No user available for vendor bills fetch');
      return;
    }

    try {
      console.log('Fetching vendor bills with BigCapital service for user:', user.id);

      const fetchedBills = await bigCapitalService.getVendorBills(user.id);
      setVendorBills(fetchedBills);
      toast.success(`Berhasil memuat ${fetchedBills.length} tagihan vendor`);
    } catch (err) {
      console.error('Error fetching vendor bills:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast.error(`Gagal memuat tagihan vendor: ${errorMessage}`);
      setVendorBills([]);
    }
  };

  // Function to fetch journal entries
  const fetchJournalEntriesData = async () => {
    try {
      const accessToken = session?.access_token;
      console.log('Using access token for journal entries API request:', accessToken ? 'Token available' : 'No token');

      const fetchedJournals = await fetchJournalEntries(accessToken);
      console.log('Fetched journal entries:', fetchedJournals);

      // Validate journal entries data - now using 'amount' field
      const validJournals = fetchedJournals.filter(journal => {
        const hasValidAmount = !isNaN(Number(journal.amount)) && journal.amount !== null && journal.amount !== undefined;
        if (!hasValidAmount) {
          console.warn('Invalid journal entry amount:', journal);
        }
        return hasValidAmount;
      });

      setJournalEntries(validJournals);
      toast.success(`Berhasil memuat ${validJournals.length} jurnal umum`);
    } catch (err) {
      console.error('Error fetching journal entries:', err);
      toast.error('Gagal memuat jurnal umum');
      setJournalEntries([]);
    }
  };

  // Combined function to fetch all data
  const fetchAllData = async () => {
    setLoading(true);
    setError(null);
    try {
      await Promise.all([fetchInvoicesData(), fetchVendorBillsData(), fetchJournalEntriesData()]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch data if user is authenticated
    if (session?.access_token && user) {
      fetchAllData();
    }
  }, [session?.access_token, user]);

  // Update countdown for nearest due dates
  useEffect(() => {
    const updateCountdowns = () => {
      const today = new Date();

      // Customer countdown
      const unpaidInvoices = invoices.filter(i => !i.is_fully_paid);
      const nearestInvoice = unpaidInvoices
        .map(invoice => ({
          ...invoice,
          dueDate: new Date(invoice.due_date),
        }))
        .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())[0];

      if (nearestInvoice) {
        const timeDiff = nearestInvoice.dueDate.getTime() - today.getTime();
        if (timeDiff <= 0) {
          setCustomerCountdown('OVERDUE');
        } else {
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          if (days > 0) {
            setCustomerCountdown(`${days}d ${hours}h`);
          } else {
            setCustomerCountdown(`${hours}h`);
          }
        }
      } else {
        setCustomerCountdown('No due dates');
      }

      // Vendor countdown
      const unpaidBills = vendorBills.filter(b => !b.is_fully_paid);
      const nearestBill = unpaidBills
        .map(bill => ({
          ...bill,
          dueDate: new Date(bill.due_date),
        }))
        .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())[0];

      if (nearestBill) {
        const timeDiff = nearestBill.dueDate.getTime() - today.getTime();
        if (timeDiff <= 0) {
          setVendorCountdown('OVERDUE');
        } else {
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          if (days > 0) {
            setVendorCountdown(`${days}d ${hours}h`);
          } else {
            setVendorCountdown(`${hours}h`);
          }
        }
      } else {
        setVendorCountdown('No due dates');
      }
    };

    updateCountdowns();
    const interval = setInterval(updateCountdowns, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [invoices, vendorBills]);

  // Transform invoices to match our transaction format
  const invoiceTransactions = invoices.map((invoice) => {
    // Map invoice status to our status
    const getTransactionStatus = () => {
      if (invoice.is_fully_paid) return 'selesai';
      if (invoice.is_overdue) return 'gagal';
      if (invoice.is_partially_paid) return 'pending';
      return 'pending';
    };

    return {
      id: `invoice-${invoice.id}`,
      type: 'Pembayaran Pelanggan',
      description: `Invoice ${invoice.invoice_no}${invoice.customer.display_name ? ` dari ${invoice.customer.display_name}` : ''}`,
      amount: invoice.total,
      date: invoice.invoice_date.split('T')[0],
      time: new Date(invoice.created_at).toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      bot: 'AI Pelanggan',
      status: getTransactionStatus(),
      category: 'pemasukan',
      invoice: invoice // Store the full invoice object for details
    };
  });

  // Transform vendor bills to match our transaction format
  const vendorBillTransactions = vendorBills.map((bill) => {
    // Map bill status to our status
    const getTransactionStatus = () => {
      if (bill.is_fully_paid) return 'selesai';
      if (bill.is_overdue) return 'gagal';
      if (bill.is_partially_paid) return 'pending';
      return 'pending';
    };

    return {
      id: `bill-${bill.id}`,
      type: 'Tagihan Vendor',
      description: `${bill.bill_number || `Bill #${bill.id}`}${bill.vendor.display_name ? ` dari ${bill.vendor.display_name}` : ''}`,
      amount: -bill.total, // Negative for expenses
      date: bill.bill_date.split('T')[0],
      time: new Date(bill.created_at).toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      bot: 'AI Vendor',
      status: getTransactionStatus(),
      category: 'pengeluaran',
      bill: bill // Store the full bill object for details
    };
  });

  // Transform journal entries to transaction format
  const journalTransactions = journalEntries.map((journal) => {
    // Use the amount field directly from the API response
    const amount = Number(journal.amount) || 0;

    // Calculate total debit and credit from entries to determine category
    const totalDebit = journal.entries.reduce((sum, entry) => sum + (Number(entry.debit) || 0), 0);
    const totalCredit = journal.entries.reduce((sum, entry) => sum + (Number(entry.credit) || 0), 0);

    // Check if there are expense accounts in debit (beban di debit = pengeluaran/minus)
    const hasExpenseInDebit = journal.entries.some(entry =>
      entry.debit > 0 &&
      (entry.account.account_type === 'expense' ||
       entry.account.account_parent_type === 'expense' ||
       entry.account.account_root_type === 'expense')
    );

    // If expense in debit, it's an expense (negative/red)
    // Otherwise, follow normal debit/credit logic
    const isExpense = hasExpenseInDebit;
    const displayAmount = isExpense ? -amount : amount;

    // Debug logging
    console.log(`Journal ${journal.id}: amount=${amount}, debit=${totalDebit}, credit=${totalCredit}, hasExpenseInDebit=${hasExpenseInDebit}, displayAmount=${displayAmount}`);

    return {
      id: `journal-${journal.id}`,
      type: 'Jurnal Umum',
      description: journal.description || `Jurnal ${journal.journal_number}${journal.journal_type ? ` - ${journal.journal_type}` : ''}`,
      amount: displayAmount,
      date: journal.date ? journal.date.split('T')[0] : new Date().toISOString().split('T')[0],
      time: journal.created_at ? new Date(journal.created_at).toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      }) : '00:00',
      bot: 'AI Akuntansi',
      status: journal.is_published ? 'selesai' : 'pending',
      category: isExpense ? 'pengeluaran' : 'pemasukan',
      journal: journal // Store the full journal object for details
    };
  });

  // Combine all transactions
  const allTransactions = [...invoiceTransactions, ...vendorBillTransactions, ...journalTransactions];

  const filteredTransactions = allTransactions.filter(transaction => {
    // Search filter
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.type.toLowerCase().includes(searchTerm.toLowerCase());

    // AI filter
    const matchesAI = aiFilter === 'semua' || transaction.bot === aiFilter;

    return matchesSearch && matchesAI;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'selesai': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'gagal': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    // Handle NaN, undefined, or null values
    const safeAmount = Number(amount) || 0;
    const formatted = Math.abs(safeAmount).toLocaleString('id-ID', {
      style: 'currency',
      currency: 'IDR'
    });
    return safeAmount >= 0 ? `+${formatted}` : `-${formatted}`;
  };

  // Calculate due dates and cash flow
  const overdueInvoices = invoices.filter(i => i.is_overdue === true).length;
  const overdueVendorBills = vendorBills.filter(b => b.is_overdue === true).length;

  // Alternative calculation if is_overdue field is not reliable
  const today = new Date();
  const overdueInvoicesAlt = invoices.filter(i => {
    if (!i.due_date) return false;
    const dueDate = new Date(i.due_date);
    return dueDate < today && !i.is_fully_paid;
  }).length;

  const overdueVendorBillsAlt = vendorBills.filter(b => {
    if (!b.due_date) return false;
    const dueDate = new Date(b.due_date);
    return dueDate < today && !b.is_fully_paid;
  }).length;

  // Debug logging
  console.log('Debug Summary Cards:');
  console.log('- Invoices:', invoices.length, 'Overdue (field):', overdueInvoices, 'Overdue (calc):', overdueInvoicesAlt);
  console.log('- Vendor Bills:', vendorBills.length, 'Overdue (field):', overdueVendorBills, 'Overdue (calc):', overdueVendorBillsAlt);
  console.log('- Journal Entries:', journalEntries.length);

  // Use the alternative calculation if the field-based one returns 0
  const finalOverdueInvoices = overdueInvoices > 0 ? overdueInvoices : overdueInvoicesAlt;
  const finalOverdueVendorBills = overdueVendorBills > 0 ? overdueVendorBills : overdueVendorBillsAlt;

  // Calculate net cash flow - hanya transaksi kas yang benar-benar terjadi
  // Exclude invoice transactions (masih account receivable, bukan cash)
  // Exclude vendor bills (masih account payable, bukan cash)
  // Only include journal entries yang melibatkan akun kas
  const cashTransactions = journalTransactions.filter(transaction => {
    const journal = transaction.journal;
    // Check if journal entries involve cash accounts
    const hasCashAccount = journal.entries.some(entry =>
      entry.account.account_type === 'cash' ||
      entry.account.account_type === 'bank' ||
      entry.account.name.toLowerCase().includes('kas') ||
      entry.account.name.toLowerCase().includes('bank') ||
      entry.account.name.toLowerCase().includes('cash')
    );

    if (hasCashAccount) {
      console.log('Cash transaction found:', journal.journal_number, 'Amount:', transaction.amount);
      journal.entries.forEach(entry => {
        console.log('  - Account:', entry.account.name, 'Type:', entry.account.account_type, 'Debit:', entry.debit, 'Credit:', entry.credit);
      });
    }

    return hasCashAccount;
  });

  const cashInflow = cashTransactions
    .filter(t => t.status === 'selesai' && t.amount > 0)
    .reduce((sum, t) => sum + t.amount, 0);

  const cashOutflow = cashTransactions
    .filter(t => t.status === 'selesai' && t.amount < 0)
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const netCash = cashInflow - cashOutflow;

  console.log('Cash Flow Calculation:');
  console.log('- Cash Transactions:', cashTransactions.length);
  console.log('- Cash Inflow:', cashInflow);
  console.log('- Cash Outflow:', cashOutflow);
  console.log('- Net Cash:', netCash);

  const handleTransactionClick = (transaction: any) => {
    if (transaction.type === 'Pembayaran Pelanggan' && transaction.invoice) {
      setSelectedInvoice(transaction.invoice);
      setInvoiceDialogOpen(true);
    } else if (transaction.type === 'Tagihan Vendor' && transaction.bill) {
      setSelectedVendorBill(transaction.bill);
      setVendorBillDialogOpen(true);
    } else if (transaction.type === 'Jurnal Umum' && transaction.journal) {
      setSelectedJournalEntry(transaction.journal);
      setJournalDialogOpen(true);
    } else {
      toast.info('Detail hanya tersedia untuk transaksi pembayaran pelanggan, tagihan vendor, dan jurnal umum');
    }
  };

  const handleRefresh = () => {
    fetchAllData();
  };

  // Approval workflow functions
  const handleVerify = (transactionId: string) => {
    if (userRole !== 'staff' && userRole !== 'admin') {
      toast.error('Hanya staff yang dapat melakukan verifikasi');
      return;
    }
    toast.success('Transaksi berhasil diverifikasi');
  };

  const handleApprove = (transactionId: string) => {
    if (userRole !== 'admin') {
      toast.error('Hanya administrator yang dapat melakukan approval');
      return;
    }
    toast.success('Transaksi berhasil disetujui');
  };

  const handleReject = (transactionId: string) => {
    if (userRole !== 'admin') {
      toast.error('Hanya administrator yang dapat menolak transaksi');
      return;
    }
    toast.success('Transaksi ditolak');
  };

  // Check if user can perform actions
  const canVerify = userRole === 'staff' || userRole === 'admin';
  const canApprove = userRole === 'admin';
  const hasApprovalFeature = userPlan === 'premium' || userPlan === 'enterprise';

  return (
    <div className="w-full h-[calc(100vh-60px)] overflow-y-auto">
      <div className="max-w-4xl mx-auto p-2 sm:p-4">
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-col gap-4 mb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <h1 className="text-xl sm:text-2xl font-bold">Semua Transaksi</h1>
                {(userPlan === 'premium' || userPlan === 'enterprise') && (
                  <div className="flex items-center gap-1">
                    {userPlan === 'premium' && <Crown className="w-4 h-4 text-yellow-500" />}
                    {userPlan === 'enterprise' && <Shield className="w-4 h-4 text-blue-500" />}
                    <span className="text-xs text-muted-foreground">
                      {userPlan === 'premium' ? 'Premium' : 'Enterprise'} • Two-step approval
                    </span>
                  </div>
                )}
              </div>
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {/* Demo Switcher */}
            <DemoSwitcher
              currentRole={userRole}
              currentPlan={userPlan}
              onRoleChange={setUserRole}
              onPlanChange={setUserPlan}
              compact={true}
            />
          </div>
          
          {/* Error Message */}
          {error && (
            <Card className="mb-4 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="text-red-600 text-sm">
                  <strong>Error:</strong> {error}
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRefresh} 
                  className="mt-2"
                  disabled={loading}
                >
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
            <Card
              className="cursor-pointer hover:shadow-md transition-shadow hover:bg-gray-50"
              onClick={() => setDueDateCustomerDialogOpen(true)}
            >
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Due Date Customer</div>
                <div className="text-lg sm:text-2xl font-bold text-gray-700">
                  {customerCountdown}
                </div>
                <div className="text-xs text-muted-foreground">
                  {finalOverdueInvoices} overdue dari {invoices.filter(i => !i.is_fully_paid).length} invoice
                </div>
              </CardContent>
            </Card>
            <Card
              className="cursor-pointer hover:shadow-md transition-shadow hover:bg-gray-50"
              onClick={() => setDueDateVendorDialogOpen(true)}
            >
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Due Date Vendor</div>
                <div className="text-lg sm:text-2xl font-bold text-gray-700">
                  {vendorCountdown}
                </div>
                <div className="text-xs text-muted-foreground">
                  {finalOverdueVendorBills} overdue dari {vendorBills.filter(b => !b.is_fully_paid).length} tagihan
                </div>
              </CardContent>
            </Card>
            <Card
              className="cursor-pointer hover:shadow-md transition-shadow hover:bg-gray-50"
              onClick={() => setCashFlowDialogOpen(true)}
            >
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Net Cash</div>
                <div className="text-lg sm:text-2xl font-bold text-gray-700">
                  {formatAmount(netCash)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {cashTransactions.length} transaksi kas
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and AI Filter Tags */}
          <div className="space-y-3 mb-4 sm:mb-6">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Cari transaksi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* AI Filter Tags */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={aiFilter === 'semua' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiFilter('semua')}
                className="text-xs"
              >
                Semua AI
              </Button>
              <Button
                variant={aiFilter === 'AI Pelanggan' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiFilter('AI Pelanggan')}
                className="text-xs"
              >
                AI Pelanggan
              </Button>
              <Button
                variant={aiFilter === 'AI Vendor' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiFilter('AI Vendor')}
                className="text-xs"
              >
                AI Vendor
              </Button>
              <Button
                variant={aiFilter === 'AI Akuntansi' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiFilter('AI Akuntansi')}
                className="text-xs"
              >
                AI Akuntansi
              </Button>
            </div>
          </div>
        </div>

        {/* Transactions List */}
        <div className="space-y-2 sm:space-y-3">
          {loading && (
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Memuat data transaksi...</span>
                </div>
              </CardContent>
            </Card>
          )}

          {!loading && filteredTransactions.length === 0 && (
            <Card>
              <CardContent className="p-4 text-center text-muted-foreground">
                {searchTerm ? 'Tidak ada transaksi yang sesuai dengan pencarian' : 'Belum ada transaksi'}
              </CardContent>
            </Card>
          )}
          
          {filteredTransactions.map((transaction) => (
            <Card
              key={transaction.id}
              className={`hover:shadow-md transition-shadow ${
                (transaction.type === 'Pembayaran Pelanggan' || transaction.type === 'Tagihan Vendor' || transaction.type === 'Jurnal Umum')
                  ? 'cursor-pointer hover:bg-muted/50' : ''
              }`}
              onClick={() => handleTransactionClick(transaction)}
            >
              <CardContent className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                      <h3 className="font-semibold text-sm sm:text-base truncate">{transaction.type}</h3>
                      <div className="flex gap-1 sm:gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {transaction.bot}
                        </Badge>
                        <Badge className={`text-xs ${getStatusColor(transaction.status)}`}>
                          {transaction.status}
                        </Badge>

                        {/* Approval Status Badges for Premium/Enterprise */}
                        {hasApprovalFeature && (
                          <div className="flex gap-1">
                            <Badge variant="outline" className="text-xs flex items-center gap-1">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              Verified
                            </Badge>
                            <Badge variant="outline" className="text-xs flex items-center gap-1">
                              <Clock className="w-3 h-3 text-yellow-500" />
                              Pending Approval
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                    <p className="text-xs sm:text-sm text-muted-foreground mb-2 line-clamp-2">
                      {transaction.description}
                    </p>
                    <div className="text-xs text-muted-foreground">
                      {transaction.date} pada {transaction.time}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2 sm:text-right">
                    <div className="flex justify-between sm:block">
                      <div className="sm:hidden text-xs text-muted-foreground">Jumlah:</div>
                      <div className="text-base sm:text-lg font-bold text-gray-700">
                        {formatAmount(Math.abs(transaction.amount))}
                      </div>
                    </div>

                    {/* Approval Buttons for Premium/Enterprise */}
                    {hasApprovalFeature && (
                      <div className="flex gap-1 justify-end">
                        {canVerify && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleVerify(transaction.id);
                            }}
                            className="text-xs h-6 px-2"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Verify
                          </Button>
                        )}
                        {canApprove && (
                          <>
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApprove(transaction.id);
                              }}
                              className="text-xs h-6 px-2"
                            >
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleReject(transaction.id);
                              }}
                              className="text-xs h-6 px-2"
                            >
                              <XCircle className="w-3 h-3 mr-1" />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Data Stats */}
        {!loading && (invoices.length > 0 || vendorBills.length > 0 || journalEntries.length > 0) && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Statistik Data:</strong>
                {invoices.length > 0 && (
                  <span> Invoice: {invoices.length} total |
                  Terbayar: {invoices.filter(i => i.is_fully_paid).length} |
                  Pending: {invoices.filter(i => !i.is_fully_paid && !i.is_overdue).length} |
                  Overdue: {invoices.filter(i => i.is_overdue).length}</span>
                )}
                {vendorBills.length > 0 && (
                  <span> | Tagihan Vendor: {vendorBills.length} total |
                  Terbayar: {vendorBills.filter(b => b.is_fully_paid).length} |
                  Pending: {vendorBills.filter(b => !b.is_fully_paid && !b.is_overdue).length} |
                  Overdue: {vendorBills.filter(b => b.is_overdue).length}</span>
                )}
                {journalEntries.length > 0 && (
                  <span> | Jurnal Umum: {journalEntries.length} total</span>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <InvoiceDetailDialog
        invoice={selectedInvoice}
        open={invoiceDialogOpen}
        onOpenChange={setInvoiceDialogOpen}
      />

      <VendorBillDetailDialog
        vendorBill={selectedVendorBill}
        open={vendorBillDialogOpen}
        onOpenChange={setVendorBillDialogOpen}
      />

      <JournalDetailDialog
        journalEntry={selectedJournalEntry}
        open={journalDialogOpen}
        onOpenChange={setJournalDialogOpen}
      />

      <DueDateCustomerDialog
        invoices={invoices}
        open={dueDateCustomerDialogOpen}
        onOpenChange={setDueDateCustomerDialogOpen}
      />

      <DueDateVendorDialog
        vendorBills={vendorBills}
        open={dueDateVendorDialogOpen}
        onOpenChange={setDueDateVendorDialogOpen}
      />

      <CashFlowDialog
        cashTransactions={cashTransactions}
        cashInflow={cashInflow}
        cashOutflow={cashOutflow}
        netCash={netCash}
        open={cashFlowDialogOpen}
        onOpenChange={setCashFlowDialogOpen}
      />
    </div>
  );
};
