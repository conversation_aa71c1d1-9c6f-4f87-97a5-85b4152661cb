import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types (you can expand this as needed)
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string | null;
          full_name: string | null;
          avatar_url: string | null;
          website: string | null;
          bio: string | null;
          whatsapp_number: string | null;
          phone_number: string | null;
          country_code: string | null;
          is_whatsapp_verified: boolean | null;
          whatsapp_verification_code: string | null;
          whatsapp_verification_expires_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          bio?: string | null;
          whatsapp_number?: string | null;
          phone_number?: string | null;
          country_code?: string | null;
          is_whatsapp_verified?: boolean | null;
          whatsapp_verification_code?: string | null;
          whatsapp_verification_expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          bio?: string | null;
          whatsapp_number?: string | null;
          phone_number?: string | null;
          country_code?: string | null;
          is_whatsapp_verified?: boolean | null;
          whatsapp_verification_code?: string | null;
          whatsapp_verification_expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      report_settings: {
        Row: {
          id: string;
          user_id: string;
          report_type: string;
          from_date: string;
          to_date: string;
          company_name: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          report_type: string;
          from_date: string;
          to_date: string;
          company_name?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          report_type?: string;
          from_date?: string;
          to_date?: string;
          company_name?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_current_user_profile: {
        Args: Record<PropertyKey, never>;
        Returns: {
          id: string;
          email: string;
          full_name: string;
          avatar_url: string;
          website: string;
          bio: string;
          created_at: string;
          updated_at: string;
        }[];
      };
      update_current_user_profile: {
        Args: {
          new_full_name?: string;
          new_avatar_url?: string;
          new_website?: string;
          new_bio?: string;
        };
        Returns: Database['public']['Tables']['profiles']['Row'];
      };
      save_report_settings: {
        Args: {
          p_report_type: string;
          p_from_date: string;
          p_to_date: string;
          p_company_name?: string;
        };
        Returns: Database['public']['Tables']['report_settings']['Row'];
      };
      get_user_report_settings: {
        Args: {
          p_report_type?: string;
        };
        Returns: {
          id: string;
          report_type: string;
          from_date: string;
          to_date: string;
          company_name: string;
          created_at: string;
          updated_at: string;
        }[];
      };
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Helper functions for common operations
export const supabaseHelpers = {
  // Get current user profile
  async getCurrentUserProfile() {
    const { data, error } = await supabase.rpc('get_current_user_profile');
    if (error) throw error;
    return data;
  },

  // Update current user profile
  async updateCurrentUserProfile(updates: {
    new_full_name?: string;
    new_avatar_url?: string;
    new_website?: string;
    new_bio?: string;
  }) {
    const { data, error } = await supabase.rpc('update_current_user_profile', updates);
    if (error) throw error;
    return data;
  },

  // Save report settings
  async saveReportSettings(reportType: string, fromDate: string, toDate: string, companyName?: string) {
    const { data, error } = await supabase.rpc('save_report_settings', {
      p_report_type: reportType,
      p_from_date: fromDate,
      p_to_date: toDate,
      p_company_name: companyName
    });
    if (error) throw error;
    return data;
  },

  // Get user report settings
  async getUserReportSettings(reportType?: string) {
    const { data, error } = await supabase.rpc('get_user_report_settings', {
      p_report_type: reportType
    });
    if (error) throw error;
    return data;
  }
};

export default supabase;
