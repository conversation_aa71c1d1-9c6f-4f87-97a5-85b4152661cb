# Fitur List Transaksi Umum (Journal Entries)

## Deskripsi
Fitur ini menambahkan kemampuan untuk menampilkan dan melihat detail transaksi umum (journal entries) yang diambil dari API `https://wabot-n8n.libslm.easypanel.host/webhook/transync/journals/list`.

## Implementasi

### 1. Interface dan Tipe Data
- **JournalEntry**: Interface TypeScript yang mendefinisikan struktur data journal entry
- Mendukung semua field yang diperlukan termasuk entries, accounts, dan contact information

### 2. API Integration
- **fetchJournalEntries()**: Fungsi untuk mengambil data dari API journals/list
- Menggunakan authorization header dengan token dari Supabase auth
- Menangani berbagai format response dari API
- Error handling yang komprehensif

### 3. Komponen UI

#### TransactionsTab (Updated)
- Menambahkan state management untuk journal entries
- Integrasi dengan existing transaction list
- Transformasi data journal entries ke format transaction
- Update perhitungan total income/expense
- Clickable journal entries untuk detail view

#### JournalDetailDialog (New)
- Dialog detail khusus untuk journal entries
- Menampilkan informasi lengkap journal entry
- Tabel entries dengan debit/credit
- Validasi balance (seimbang/tidak seimbang)
- Informasi account dan contact

### 4. Fitur Utama

#### List View
- Journal entries ditampilkan dalam list transaksi umum
- Kategori otomatis berdasarkan debit/credit balance
- Status selalu "selesai" karena journal entries sudah final
- Bot identifier: "AI Akuntansi"

#### Detail View
- Informasi jurnal lengkap (nomor, tipe, tanggal, referensi)
- Ringkasan saldo (total debit, kredit, selisih)
- Tabel entries dengan detail account
- Informasi contact jika ada
- Validasi balance dengan indikator visual

#### Integration
- Terintegrasi dengan existing invoice dan vendor bill transactions
- Update statistik data untuk include journal entries
- Consistent UI/UX dengan dialog lainnya

## Penggunaan

### Untuk User
1. Buka tab "Transaksi"
2. Journal entries akan muncul dalam list dengan label "Jurnal Umum"
3. Klik pada journal entry untuk melihat detail
4. Detail dialog menampilkan semua informasi journal entry

### Untuk Developer
```typescript
// Fetch journal entries
const journals = await fetchJournalEntries(accessToken);

// Transform to transaction format
const journalTransactions = journals.map(journal => ({
  id: `journal-${journal.id}`,
  type: 'Jurnal Umum',
  description: journal.description || `Jurnal ${journal.journal_number}`,
  amount: Math.abs(journal.total_debit - journal.total_credit),
  category: journal.total_debit > journal.total_credit ? 'pemasukan' : 'pengeluaran',
  journal: journal
}));
```

## API Endpoint
- **URL**: `https://wabot-n8n.libslm.easypanel.host/webhook/transync/journals/list`
- **Method**: GET
- **Headers**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer {supabase_access_token}`

## Response Format
API mendukung berbagai format response:
- Array langsung: `[{journal1}, {journal2}, ...]`
- Wrapped dalam data: `{data: [{journal1}, {journal2}, ...]}`
- Wrapped dalam journals: `{journals: [{journal1}, {journal2}, ...]}`

## Error Handling
- Network errors dengan pesan user-friendly
- API errors dengan status code handling
- Fallback ke empty array jika response tidak sesuai format
- Toast notifications untuk feedback user
- **NaN Protection**: Validasi data untuk mencegah NaN pada amount transaksi
- **Data Validation**: Filter journal entries dengan amount yang tidak valid
- **Safe Parsing**: Menggunakan Number() dengan fallback ke 0 untuk nilai null/undefined

## Files Modified/Created
1. `src/components/tabs/TransactionsTab.tsx` - Updated
2. `src/components/JournalDetailDialog.tsx` - New
3. Interface definitions added for JournalEntry

## Testing
- Aplikasi dapat dijalankan dengan `npm run dev`
- Journal entries akan muncul jika API mengembalikan data
- Dialog detail dapat dibuka dengan mengklik journal entry
- Responsive design untuk mobile dan desktop

## Bug Fixes
### v1.1 - Fixed NaN Amount Issue
- **Problem**: Journal entries menampilkan "NaN" sebagai amount transaksi
- **Root Cause**: API response dengan nilai null/undefined untuk total_debit/total_credit
- **Solution**:
  - Implementasi safe parsing dengan `Number(value) || 0`
  - Validasi data sebelum transformasi
  - Menggunakan `Math.max(debit, credit)` sebagai display amount
  - Enhanced error handling dalam `formatAmount` function
- **Result**: Semua journal entries sekarang menampilkan amount yang valid

### v1.2 - Updated Interface to Match Actual API Response
- **Problem**: Interface tidak sesuai dengan struktur response API yang sebenarnya
- **Root Cause**: API response menggunakan field `amount` bukan `total_debit`/`total_credit`
- **Solution**:
  - Update interface JournalEntry untuk match actual response structure
  - Menggunakan field `amount` langsung dari API response
  - Calculate debit/credit totals dari entries array untuk kategori
  - Update JournalDetailDialog untuk menampilkan data yang benar
  - Menambahkan informasi status published/draft
- **Result**: Journal entries sekarang menampilkan data yang akurat sesuai API response

### v1.3 - Fixed Expense Logic and Summary Cards
- **Problem**:
  1. Beban di debit tidak ditampilkan sebagai minus (merah)
  2. Summary cards menampilkan profit instead of due dates dan net cash
- **Root Cause**: Logika kategori tidak mempertimbangkan account type expense
- **Solution**:
  - Implementasi deteksi expense accounts di debit position
  - Jika ada beban di debit, amount menjadi negative (merah)
  - Update summary cards: Due Date Customer, Due Date Vendor, Net Cash
  - Net Cash = kas masuk - kas keluar (bukan profit)
- **Result**:
  - Expense entries ditampilkan dengan notasi minus (merah)
  - Summary cards menampilkan informasi yang relevan untuk cash flow

### v1.4 - Fixed Cash Flow Logic
- **Problem**: Net cash menghitung account receivable sebagai cash
- **Root Cause**: Pendapatan yang masih berupa piutang belum menjadi kas
- **Solution**:
  - Filter hanya transaksi yang melibatkan akun kas/bank
  - Exclude invoice transactions (account receivable)
  - Exclude vendor bills (account payable)
  - Only include journal entries dengan cash/bank accounts
- **Result**: Net cash hanya menghitung transaksi kas yang benar-benar terjadi

### v2.0 - Enhanced UI with Clickable Summary Cards and Debit/Credit Display
- **New Features**:
  1. **Clickable Summary Cards dengan Countdown**
     - Due Date Customer: Menampilkan countdown ke due date terdekat
     - Due Date Vendor: Menampilkan countdown ke due date terdekat
     - Net Cash: Clickable untuk melihat detail cash flow
  2. **Dialog Components**
     - DueDateCustomerDialog: Detail invoice dengan countdown real-time
     - DueDateVendorDialog: Detail vendor bills dengan countdown real-time
     - CashFlowDialog: Detail cash inflow dan outflow
  3. **Neutral Debit/Credit Display**
     - Journal entries menampilkan total debit dan credit
     - Neutral gray color untuk semua amounts
     - Clean, professional appearance
     - No color coding or category tags
- **Result**: UI yang lebih interaktif dengan tampilan yang neutral dan professional

### v2.1 - Simplified to Neutral Design
- **Changes**:
  - Removed P&L and Balance Sheet categorization
  - Removed color coding for positive/negative amounts
  - Simplified to show only Debit and Credit totals
  - Used neutral gray color for all journal entry amounts
  - Maintained clickable functionality for summary cards
- **Result**: Clean, neutral interface focusing on basic accounting information

### v2.2 - Final Simplification and AI Filters
- **Major Changes**:
  1. **Single Amount Display**
     - Removed double debit/credit display in transaction list
     - Show only one neutral amount per transaction (absolute value)
     - Maintained debit/credit details in journal detail dialog
  2. **Neutral Summary Cards**
     - All summary card amounts in neutral gray color
     - Removed green/red color coding
     - Consistent neutral design throughout
  3. **AI Filter System**
     - Added dropdown filter for AI types
     - Filter options: Semua AI, AI Pelanggan, AI Vendor, AI Akuntansi, AI Kas Kecil
     - Real-time filtering of transactions by AI type
  4. **Data Cleanup**
     - Removed all dummy/mock data (MOCK_OTHER_TRANSACTIONS)
     - Only show real data from APIs
     - Cleaner, more accurate transaction list
- **Result**: Streamlined, professional interface with effective filtering and neutral design

### v2.3 - Improved Layout with Tag-based Filtering
- **Layout Changes**:
  1. **Repositioned Search and Filter**
     - Moved search bar below summary cards
     - Better visual hierarchy and flow
  2. **Tag-based AI Filter**
     - Replaced dropdown with clickable button tags
     - 4 filter buttons: Semua AI, AI Pelanggan, AI Vendor, AI Akuntansi
     - Active state highlighting for selected filter
     - More intuitive and accessible filtering
  3. **Improved Spacing**
     - Better spacing between sections
     - Cleaner visual separation
     - Enhanced mobile responsiveness
- **Result**: More intuitive layout with better user experience and easier filtering

## Future Enhancements
- Filter berdasarkan tipe journal
- Export journal entries
- Edit/create journal entries
- Advanced search dalam journal entries
- Bulk operations untuk journal entries
