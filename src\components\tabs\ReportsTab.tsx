
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Download, Calendar } from 'lucide-react';

export const ReportsTab = () => {
  const [companyName, setCompanyName] = useState('Nama Perusahaan Anda');
  const [reportType, setReportType] = useState('');
  const [selectedDate, setSelectedDate] = useState('2025-06-15');

  const reportTypes = [
    { value: 'profit-loss', label: 'Laporan Laba Rugi' },
    { value: 'balance-sheet', label: 'Neraca' },
    { value: 'cash-flow', label: 'Laporan A<PERSON>' },
    { value: 'transaction-summary', label: 'Ringkas<PERSON>i' },
    { value: 'petty-cash', label: '<PERSON>por<PERSON>' },
    { value: 'vendor-summary', label: 'Ringkasan Vendor' },
    { value: 'customer-summary', label: 'Ringkasan Pelanggan' }
  ];

  const handleGenerateReport = () => {
    console.log('Membuat laporan:', { reportType, companyName, selectedDate });
    // Di sini Anda akan mengintegrasikan dengan logika pembuatan laporan
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="text-center mb-8">
        <FileText className="w-12 h-12 mx-auto mb-4 text-primary" />
        <h1 className="text-3xl font-bold mb-2">Generator Laporan Keuangan</h1>
        <div className="flex items-center justify-center gap-4 text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-muted rounded"></div>
            <span>{companyName}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>{selectedDate}</span>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Pilih Jenis Laporan</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="company-name">Nama Perusahaan</Label>
            <Input
              id="company-name"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="Masukkan nama perusahaan"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="report-type">Jenis Laporan</Label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih laporan keuangan" />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="report-date">Tanggal Laporan</Label>
            <Input
              id="report-date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>

          {reportType && (
            <Card className="bg-muted/50">
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2">Detail Laporan</h3>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Data akan diambil dari transaksi chat AI</li>
                  <li>• Laporan dibuat per tanggal saat ini</li>
                  <li>• Format PDF untuk mudah dibagikan dan dicetak</li>
                  <li>• Format akuntansi profesional</li>
                </ul>
              </CardContent>
            </Card>
          )}

          <Button 
            className="w-full" 
            size="lg" 
            onClick={handleGenerateReport}
            disabled={!reportType}
          >
            <Download className="w-4 h-4 mr-2" />
            Buat & Unduh Laporan PDF
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
