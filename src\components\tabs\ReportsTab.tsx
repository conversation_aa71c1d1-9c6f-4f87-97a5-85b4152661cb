
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Download, Calendar, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

// Interface for balance sheet data based on API response
interface BalanceSheetColumn {
  key: string;
  label: string;
  cell_index: number;
  children?: any[];
}

interface BalanceSheetTable {
  columns: BalanceSheetColumn[];
  rows: any[];
}

interface BalanceSheetQuery {
  display_columns_type: string;
  display_columns_by: string;
  from_date: string;
  to_date: string;
  number_format: {
    precision: number;
    divide_on_1000: boolean;
    show_zero: boolean;
    format_money: string;
    negative_format: string;
  };
  basis: string;
  account_ids: any[];
  accounts_ids: any[];
}

interface BalanceSheetMeta {
  organization_name: string;
  base_currency: string;
  date_format: string;
  sheet_name: string;
  formatted_as_date: string;
  formatted_date_range: string;
}

interface BalanceSheetData {
  table: BalanceSheetTable;
  query: BalanceSheetQuery;
  meta: BalanceSheetMeta;
}

export const ReportsTab = () => {
  const { session, user } = useAuth();
  const [companyName, setCompanyName] = useState('Nama Perusahaan Anda');
  const [reportType, setReportType] = useState('');
  const [fromDate, setFromDate] = useState('2025-06-01');
  const [toDate, setToDate] = useState('2025-06-30');
  const [loading, setLoading] = useState(false);
  const [balanceSheetData, setBalanceSheetData] = useState<BalanceSheetData | null>(null);

  const reportTypes = [
    { value: 'profit-loss', label: 'Laporan Laba Rugi' },
    { value: 'balance-sheet', label: 'Neraca' },
    { value: 'cash-flow', label: 'Laporan Arus Kas' },
    { value: 'transaction-summary', label: 'Ringkasan Transaksi' },
    { value: 'petty-cash', label: 'Laporan Kas Kecil' },
    { value: 'vendor-summary', label: 'Ringkasan Vendor' },
    { value: 'customer-summary', label: 'Ringkasan Pelanggan' }
  ];

  // Function to save report dates to Supabase
  const saveReportDates = async (from_date: string, to_date: string, report_type: string) => {
    if (!user) {
      toast.error('User tidak ditemukan');
      return;
    }

    try {
      const { error } = await supabase
        .from('report_settings')
        .upsert({
          user_id: user.id,
          from_date,
          to_date,
          report_type,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,report_type'
        });

      if (error) {
        console.error('Error saving report dates:', error);
        toast.error('Gagal menyimpan pengaturan laporan');
      } else {
        console.log('Report dates saved successfully');
      }
    } catch (error) {
      console.error('Error saving report dates:', error);
      toast.error('Gagal menyimpan pengaturan laporan');
    }
  };

  // Function to fetch balance sheet data
  const fetchBalanceSheet = async () => {
    if (!session?.access_token) {
      toast.error('Token akses tidak ditemukan');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('https://wabot-n8n.libslm.easypanel.host/webhook/b7c617da-1931-4bfe-a581-ad4c27cc13b8', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('Raw API response:', responseData);

      // Handle array response - take first element
      const data = Array.isArray(responseData) ? responseData[0] : responseData;
      setBalanceSheetData(data);
      toast.success('Data neraca berhasil dimuat');
      console.log('Processed balance sheet data:', data);
    } catch (error) {
      console.error('Error fetching balance sheet:', error);
      toast.error('Gagal memuat data neraca');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    if (!reportType || !fromDate || !toDate) {
      toast.error('Mohon lengkapi semua field yang diperlukan');
      return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
      toast.error('Tanggal dari tidak boleh lebih besar dari tanggal hingga');
      return;
    }

    console.log('Membuat laporan:', { reportType, companyName, fromDate, toDate });

    // Save report dates to Supabase
    await saveReportDates(fromDate, toDate, reportType);

    // If balance sheet is selected, fetch the data
    if (reportType === 'balance-sheet') {
      await fetchBalanceSheet();
    } else {
      toast.success(`Laporan ${reportTypes.find(r => r.value === reportType)?.label} sedang diproses`);
      // Di sini Anda akan mengintegrasikan dengan logika pembuatan laporan lainnya
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="text-center mb-8">
        <FileText className="w-12 h-12 mx-auto mb-4 text-primary" />
        <h1 className="text-3xl font-bold mb-2">Generator Laporan Keuangan</h1>
        <div className="flex items-center justify-center gap-4 text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-muted rounded"></div>
            <span>{companyName}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>{fromDate} - {toDate}</span>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Pilih Jenis Laporan</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="company-name">Nama Perusahaan</Label>
            <Input
              id="company-name"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="Masukkan nama perusahaan"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="report-type">Jenis Laporan</Label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih laporan keuangan" />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="from-date">Tanggal Dari</Label>
              <Input
                id="from-date"
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="to-date">Tanggal Hingga</Label>
              <Input
                id="to-date"
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
              />
            </div>
          </div>

          {reportType && (
            <Card className="bg-muted/50">
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2">Detail Laporan</h3>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Data akan diambil dari transaksi chat AI</li>
                  <li>• Laporan dibuat berdasarkan rentang tanggal yang dipilih</li>
                  <li>• Format PDF untuk mudah dibagikan dan dicetak</li>
                  <li>• Format akuntansi profesional</li>
                  {reportType === 'balance-sheet' && (
                    <li>• Data neraca akan dimuat dari API BigCapital</li>
                  )}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Balance Sheet Data Display */}
          {reportType === 'balance-sheet' && balanceSheetData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{balanceSheetData.meta?.sheet_name || 'Neraca'}</span>
                  <span className="text-sm font-normal text-muted-foreground">
                    {balanceSheetData.meta?.organization_name}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Meta Information */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-semibold">Periode:</span>
                      <p>{balanceSheetData.meta?.formatted_date_range || `${fromDate} - ${toDate}`}</p>
                    </div>
                    <div>
                      <span className="font-semibold">Mata Uang:</span>
                      <p>{balanceSheetData.meta?.base_currency || 'IDR'}</p>
                    </div>
                    <div>
                      <span className="font-semibold">Basis:</span>
                      <p className="capitalize">{balanceSheetData.query?.basis || 'Cash'}</p>
                    </div>
                  </div>
                </div>

                {/* Table Structure */}
                {balanceSheetData.table && (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-100">
                          {balanceSheetData.table.columns.map((column, index) => (
                            <th
                              key={index}
                              className="border border-gray-300 px-4 py-2 text-left font-semibold"
                            >
                              {column.label}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {balanceSheetData.table.rows.length > 0 ? (
                          balanceSheetData.table.rows.map((row, rowIndex) => (
                            <tr key={rowIndex} className="hover:bg-gray-50">
                              {balanceSheetData.table.columns.map((column, colIndex) => (
                                <td
                                  key={colIndex}
                                  className="border border-gray-300 px-4 py-2"
                                >
                                  {row[column.key] || '-'}
                                </td>
                              ))}
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td
                              colSpan={balanceSheetData.table.columns.length}
                              className="border border-gray-300 px-4 py-8 text-center text-muted-foreground"
                            >
                              Tidak ada data untuk periode yang dipilih
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}

                {/* API Info */}
                <div className="mt-4 text-sm text-muted-foreground">
                  <p>✅ Data berhasil dimuat dari API BigCapital</p>
                  <p>📊 Format: {balanceSheetData.query?.display_columns_type || 'Total'}</p>
                  <p>📅 Periode Query: {balanceSheetData.query?.from_date} - {balanceSheetData.query?.to_date}</p>
                </div>
              </CardContent>
            </Card>
          )}

          <Button
            className="w-full"
            size="lg"
            onClick={handleGenerateReport}
            disabled={!reportType || !fromDate || !toDate || loading}
          >
            {loading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                {reportType === 'balance-sheet' ? 'Memuat Data Neraca...' : 'Memproses Laporan...'}
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                {reportType === 'balance-sheet' ? 'Muat Data Neraca' : 'Buat & Unduh Laporan PDF'}
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
