import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Send, 
  RefreshCw, 
  Wallet, 
  MessageCircle,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import { PettyCashManagement } from '@/components/PettyCashManagement';
import { DemoSwitcher } from '@/components/DemoSwitcher';

interface PettyCashAIChatProps {
  onBack: () => void;
  userPlan?: 'free' | 'premium' | 'enterprise';
  userRole?: 'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash' | 'staff';
}

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  transactions?: PettyCashTransaction[];
}

interface PettyCashTransaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  date: string;
  createdBy: string;
  status: 'pending' | 'approved' | 'rejected';
}

export const PettyCashAIChat: React.FC<PettyCashAIChatProps> = ({
  onBack,
  userPlan: initialUserPlan = 'free',
  userRole: initialUserRole = 'general'
}) => {
  const [activeTab, setActiveTab] = useState('chat');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Demo state for role and plan management
  const [userRole, setUserRole] = useState<'admin' | 'purchase' | 'sales' | 'general' | 'petty_cash' | 'staff'>(initialUserRole);
  const [userPlan, setUserPlan] = useState<'free' | 'premium' | 'enterprise'>(initialUserPlan);

  // Mock petty cash data
  const [pettyCashData] = useState<PettyCashTransaction[]>([
    {
      id: '1',
      type: 'expense',
      amount: 50000,
      description: 'Bensin motor untuk pengiriman dokumen',
      category: 'Transportasi',
      date: '2024-06-19',
      createdBy: 'John Doe',
      status: 'approved'
    },
    {
      id: '2',
      type: 'expense',
      amount: 25000,
      description: 'Makan siang meeting dengan klien',
      category: 'Konsumsi',
      date: '2024-06-18',
      createdBy: 'Jane Smith',
      status: 'pending'
    },
    {
      id: '3',
      type: 'income',
      amount: 100000,
      description: 'Pengembalian uang muka project',
      category: 'Pengembalian',
      date: '2024-06-17',
      createdBy: 'Bob Wilson',
      status: 'approved'
    }
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    const welcomeMessage = userPlan === 'free' 
      ? 'Halo! Saya AI Kas Kecil. Fitur ini hanya tersedia untuk pengguna Premium dan Enterprise. Upgrade sekarang untuk mengakses manajemen kas kecil yang lengkap!'
      : 'Halo! Saya AI Kas Kecil. Saya dapat membantu Anda mengelola kas kecil, transaksi harian, dan pengeluaran operasional. Ketik "saldo" untuk melihat saldo kas, "transaksi" untuk melihat riwayat, atau gunakan tab Management untuk mengelola kas kecil.';

    setMessages([{
      id: '1',
      text: welcomeMessage,
      isUser: false,
      timestamp: new Date(),
    }]);
  }, [userPlan]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sendingMessage) return;
    
    const userMessage = messageInput.trim().toLowerCase();
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageInput,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setMessageInput('');
    setSendingMessage(true);

    try {
      let aiResponse: ChatMessage;

      if (userPlan === 'free') {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Maaf, fitur AI Kas Kecil hanya tersedia untuk pengguna Premium dan Enterprise. Upgrade sekarang untuk mengakses:\n\n• Manajemen kas kecil\n• Tracking pengeluaran\n• Approval workflow\n• Laporan kas harian\n• Dan masih banyak lagi!',
          isUser: false,
          timestamp: new Date(),
        };
      } else if (userMessage.includes('saldo') || userMessage.includes('balance') || userMessage.includes('kas')) {
        const totalBalance = pettyCashData
          .filter(t => t.status === 'approved')
          .reduce((sum, t) => sum + (t.type === 'income' ? t.amount : -t.amount), 0);
        
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Saldo kas kecil saat ini adalah ${formatCurrency(totalBalance)}.\n\nRincian:\n• Total Pemasukan: ${formatCurrency(pettyCashData.filter(t => t.type === 'income' && t.status === 'approved').reduce((sum, t) => sum + t.amount, 0))}\n• Total Pengeluaran: ${formatCurrency(pettyCashData.filter(t => t.type === 'expense' && t.status === 'approved').reduce((sum, t) => sum + t.amount, 0))}\n• Transaksi Pending: ${pettyCashData.filter(t => t.status === 'pending').length}`,
          isUser: false,
          timestamp: new Date(),
        };
      } else if (userMessage.includes('transaksi') || userMessage.includes('riwayat') || userMessage.includes('history')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Berikut adalah riwayat transaksi kas kecil terbaru (${pettyCashData.length} transaksi):`,
          isUser: false,
          timestamp: new Date(),
          transactions: pettyCashData,
        };
      } else if (userMessage.includes('pending') || userMessage.includes('approval')) {
        const pendingTransactions = pettyCashData.filter(t => t.status === 'pending');
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Ada ${pendingTransactions.length} transaksi yang menunggu persetujuan:`,
          isUser: false,
          timestamp: new Date(),
          transactions: pendingTransactions,
        };
      } else if (userMessage.includes('help') || userMessage.includes('bantuan')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Saya dapat membantu Anda dengan:\n\n• Melihat saldo kas (ketik "saldo")\n• Melihat riwayat transaksi (ketik "transaksi")\n• Melihat transaksi pending (ketik "pending")\n• Mengelola kas kecil (gunakan tab Management)\n• Laporan kas harian\n• Approval transaksi\n\nApa yang ingin Anda lakukan?',
          isUser: false,
          timestamp: new Date(),
        };
      } else {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Maaf, saya belum memahami permintaan Anda. Silakan ketik "help" untuk melihat apa yang bisa saya bantu, atau ketik "saldo" untuk melihat saldo kas kecil.',
          isUser: false,
          timestamp: new Date(),
        };
      }

      setTimeout(() => {
        setMessages(prev => [...prev, aiResponse]);
      }, 500);

    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Maaf, terjadi kesalahan saat memproses permintaan Anda.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  const getStatusBadge = (status: PettyCashTransaction['status']) => {
    const config = {
      pending: { label: 'Pending', variant: 'secondary' as const },
      approved: { label: 'Disetujui', variant: 'default' as const },
      rejected: { label: 'Ditolak', variant: 'destructive' as const }
    };
    return config[status];
  };

  return (
    <div className="flex flex-col h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-orange-500">
            AK
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <h3 className="font-semibold">AI Kas Kecil</h3>
          <p className="text-sm text-muted-foreground">Petty Cash Management Assistant</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => toast.success('Data kas kecil diperbarui')}
        >
          <RefreshCw className="w-4 h-4" />
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4" />
            Chat AI
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <Wallet className="w-4 h-4" />
            Management
          </TabsTrigger>
        </TabsList>

        {/* Chat Tab */}
        <TabsContent value="chat" className="flex-1 flex flex-col mt-0">
          {/* Demo Switcher for Chat */}
          <div className="p-4 border-b bg-background">
            <DemoSwitcher
              currentRole={userRole}
              currentPlan={userPlan}
              onRoleChange={setUserRole}
              onPlanChange={setUserPlan}
              compact={true}
            />
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {messages.map((message) => (
              <div key={message.id} className="space-y-2">
                <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                    message.isUser ? 'bg-orange-500 text-white' : 'bg-muted text-foreground'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                    <span className={`text-xs block mt-1 ${
                      message.isUser ? 'text-orange-100' : 'text-muted-foreground'
                    }`}>
                      {message.timestamp.toLocaleTimeString('id-ID')}
                    </span>
                  </div>
                </div>

                {/* Transaction Cards */}
                {message.transactions && message.transactions.length > 0 && (
                  <div className="space-y-2 ml-4">
                    {message.transactions.slice(0, 3).map((transaction) => (
                      <Card key={transaction.id} className="text-sm">
                        <CardContent className="p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex items-start gap-3">
                              <div className={`p-2 rounded-lg ${
                                transaction.type === 'income' ? 'bg-green-100' : 'bg-red-100'
                              }`}>
                                {transaction.type === 'income' ? (
                                  <TrendingUp className="w-4 h-4 text-green-600" />
                                ) : (
                                  <TrendingDown className="w-4 h-4 text-red-600" />
                                )}
                              </div>
                              <div>
                                <h4 className="font-semibold">{transaction.description}</h4>
                                <p className="text-xs text-muted-foreground">{transaction.category}</p>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                  <Calendar className="w-3 h-3" />
                                  {formatDate(transaction.date)}
                                  <User className="w-3 h-3" />
                                  {transaction.createdBy}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className={`font-semibold ${
                                transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                              </p>
                              <Badge {...getStatusBadge(transaction.status)} className="mt-1">
                                {getStatusBadge(transaction.status).label}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {message.transactions.length > 3 && (
                      <p className="text-xs text-muted-foreground ml-2">
                        +{message.transactions.length - 3} transaksi lainnya
                      </p>
                    )}
                  </div>
                )}
              </div>
            ))}

            {sendingMessage && (
              <div className="flex justify-start">
                <div className="bg-muted px-3 py-2 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t bg-background">
            <div className="flex gap-2 items-center">
              <Input
                placeholder="Ketik pesan Anda..."
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                className="flex-1"
                disabled={sendingMessage}
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || sendingMessage}
                className="shrink-0"
              >
                <Send className="w-4 h-4 mr-2" />
                {sendingMessage ? 'Mengirim...' : 'Kirim'}
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="flex-1 flex flex-col mt-0">
          <div className="flex-1 overflow-y-auto">
            {/* Demo Switcher for Management */}
            <div className="p-4 border-b bg-background">
              <DemoSwitcher
                currentRole={userRole}
                currentPlan={userPlan}
                onRoleChange={setUserRole}
                onPlanChange={setUserPlan}
                compact={true}
              />
            </div>

            <PettyCashManagement
              userPlan={userPlan}
              userRole={userRole}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
