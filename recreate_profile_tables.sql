-- Recreate Profile Tables for Supabase
-- Run these SQL statements in your Supabase SQL Editor

-- 1. Create profiles table with WhatsApp number fields
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    website TEXT,
    bio TEXT,
    whatsapp_number TEXT,
    phone_number TEXT,
    country_code TEXT DEFAULT '+62',
    is_whatsapp_verified BOOLEAN DEFAULT FALSE,
    whatsapp_verification_code TEXT,
    whatsapp_verification_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Enable Row Level Security (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS policies for profiles table
-- Policy: Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

-- Policy: Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Policy: Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy: Users can delete their own profile
CREATE POLICY "Users can delete own profile" ON public.profiles
    FOR DELETE USING (auth.uid() = id);

-- 4. Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, whatsapp_number, phone_number)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'whatsapp_number',
        NEW.raw_user_meta_data->>'phone_number'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Create function to handle profile updates
CREATE OR REPLACE FUNCTION public.handle_profile_updated()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger to automatically update updated_at timestamp
DROP TRIGGER IF EXISTS on_profile_updated ON public.profiles;
CREATE TRIGGER on_profile_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_profile_updated();

-- 8. Insert profile for existing users (if any)
INSERT INTO public.profiles (id, email, full_name, whatsapp_number, phone_number)
SELECT
    id,
    email,
    COALESCE(raw_user_meta_data->>'full_name', email) as full_name,
    raw_user_meta_data->>'whatsapp_number' as whatsapp_number,
    raw_user_meta_data->>'phone_number' as phone_number
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- 9. Create indexes for better performance
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles(email);
CREATE INDEX IF NOT EXISTS profiles_whatsapp_number_idx ON public.profiles(whatsapp_number);
CREATE INDEX IF NOT EXISTS profiles_phone_number_idx ON public.profiles(phone_number);
CREATE INDEX IF NOT EXISTS profiles_created_at_idx ON public.profiles(created_at);

-- 10. Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- 11. Optional: Create a view for easier profile management
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT
    p.id,
    p.email,
    p.full_name,
    p.avatar_url,
    p.website,
    p.bio,
    p.whatsapp_number,
    p.phone_number,
    p.country_code,
    p.is_whatsapp_verified,
    p.created_at,
    p.updated_at,
    u.email_confirmed_at,
    u.last_sign_in_at
FROM public.profiles p
LEFT JOIN auth.users u ON p.id = u.id;

-- Grant permissions on the view
GRANT SELECT ON public.user_profiles TO authenticated;
GRANT ALL ON public.user_profiles TO service_role;

-- 12. Create function to get current user profile
CREATE OR REPLACE FUNCTION public.get_current_user_profile()
RETURNS TABLE (
    id UUID,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    website TEXT,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.email,
        p.full_name,
        p.avatar_url,
        p.website,
        p.bio,
        p.created_at,
        p.updated_at
    FROM public.profiles p
    WHERE p.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_current_user_profile() TO authenticated;

-- 13. Create function to update current user profile
CREATE OR REPLACE FUNCTION public.update_current_user_profile(
    new_full_name TEXT DEFAULT NULL,
    new_avatar_url TEXT DEFAULT NULL,
    new_website TEXT DEFAULT NULL,
    new_bio TEXT DEFAULT NULL
)
RETURNS public.profiles AS $$
DECLARE
    updated_profile public.profiles;
BEGIN
    UPDATE public.profiles
    SET 
        full_name = COALESCE(new_full_name, full_name),
        avatar_url = COALESCE(new_avatar_url, avatar_url),
        website = COALESCE(new_website, website),
        bio = COALESCE(new_bio, bio),
        updated_at = NOW()
    WHERE id = auth.uid()
    RETURNING * INTO updated_profile;
    
    RETURN updated_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.update_current_user_profile(TEXT, TEXT, TEXT, TEXT) TO authenticated;

-- 15. Create WhatsApp verification functions
CREATE OR REPLACE FUNCTION public.update_whatsapp_number(
    new_whatsapp_number TEXT,
    new_country_code TEXT DEFAULT '+62'
)
RETURNS public.profiles AS $$
DECLARE
    updated_profile public.profiles;
BEGIN
    UPDATE public.profiles
    SET
        whatsapp_number = new_whatsapp_number,
        country_code = new_country_code,
        is_whatsapp_verified = FALSE,
        whatsapp_verification_code = NULL,
        whatsapp_verification_expires_at = NULL,
        updated_at = NOW()
    WHERE id = auth.uid()
    RETURNING * INTO updated_profile;

    RETURN updated_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.update_whatsapp_number(TEXT, TEXT) TO authenticated;

-- 16. Create function to set WhatsApp verification code
CREATE OR REPLACE FUNCTION public.set_whatsapp_verification_code(
    verification_code TEXT,
    expires_in_minutes INTEGER DEFAULT 10
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.profiles
    SET
        whatsapp_verification_code = verification_code,
        whatsapp_verification_expires_at = NOW() + (expires_in_minutes || ' minutes')::INTERVAL,
        updated_at = NOW()
    WHERE id = auth.uid();

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.set_whatsapp_verification_code(TEXT, INTEGER) TO authenticated;

-- 17. Create function to verify WhatsApp number
CREATE OR REPLACE FUNCTION public.verify_whatsapp_number(
    verification_code TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    is_valid BOOLEAN := FALSE;
BEGIN
    UPDATE public.profiles
    SET
        is_whatsapp_verified = TRUE,
        whatsapp_verification_code = NULL,
        whatsapp_verification_expires_at = NULL,
        updated_at = NOW()
    WHERE id = auth.uid()
        AND whatsapp_verification_code = verification_code
        AND whatsapp_verification_expires_at > NOW()
    RETURNING TRUE INTO is_valid;

    RETURN COALESCE(is_valid, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.verify_whatsapp_number(TEXT) TO authenticated;

-- 18. Create function to get WhatsApp verification status
CREATE OR REPLACE FUNCTION public.get_whatsapp_verification_status()
RETURNS TABLE (
    whatsapp_number TEXT,
    is_verified BOOLEAN,
    has_pending_verification BOOLEAN,
    verification_expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.whatsapp_number,
        p.is_whatsapp_verified,
        (p.whatsapp_verification_code IS NOT NULL AND p.whatsapp_verification_expires_at > NOW()) as has_pending_verification,
        p.whatsapp_verification_expires_at
    FROM public.profiles p
    WHERE p.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_whatsapp_verification_status() TO authenticated;

-- 19. Verify the table was created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 20. Show RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename = 'profiles';

-- Success message
SELECT 'Profile tables recreated successfully!' as status;
