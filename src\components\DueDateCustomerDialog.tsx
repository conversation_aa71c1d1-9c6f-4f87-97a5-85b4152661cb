import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Interface for invoice from API
interface Invoice {
  id: number;
  invoice_no: string;
  customer: {
    display_name: string;
  };
  due_date: string;
  formatted_due_date: string;
  total: number;
  total_formatted: string;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
}

interface DueDateCustomerDialogProps {
  invoices: Invoice[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const DueDateCustomerDialog: React.FC<DueDateCustomerDialogProps> = ({
  invoices,
  open,
  onOpenChange,
}) => {
  const [countdown, setCountdown] = useState<string>('');

  // Get nearest due date
  const today = new Date();
  const unpaidInvoices = invoices.filter(i => !i.is_fully_paid);
  
  const nearestInvoice = unpaidInvoices
    .map(invoice => ({
      ...invoice,
      dueDate: new Date(invoice.due_date),
      daysUntilDue: Math.ceil((new Date(invoice.due_date).getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    }))
    .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())[0];

  // Update countdown every second
  useEffect(() => {
    if (!nearestInvoice) return;

    const updateCountdown = () => {
      const now = new Date();
      const dueDate = nearestInvoice.dueDate;
      const timeDiff = dueDate.getTime() - now.getTime();

      if (timeDiff <= 0) {
        setCountdown('OVERDUE');
        return;
      }

      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      if (days > 0) {
        setCountdown(`${days}d ${hours}h ${minutes}m ${seconds}s`);
      } else if (hours > 0) {
        setCountdown(`${hours}h ${minutes}m ${seconds}s`);
      } else {
        setCountdown(`${minutes}m ${seconds}s`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [nearestInvoice]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (invoice: any) => {
    if (invoice.is_overdue) return 'bg-red-100 text-red-800';
    if (invoice.daysUntilDue <= 3) return 'bg-orange-100 text-orange-800';
    if (invoice.daysUntilDue <= 7) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = (invoice: any) => {
    if (invoice.is_overdue) return `Overdue ${Math.abs(invoice.daysUntilDue)} days`;
    if (invoice.daysUntilDue <= 0) return 'Due Today';
    return `${invoice.daysUntilDue} days left`;
  };

  // Sort invoices by due date
  const sortedInvoices = unpaidInvoices
    .map(invoice => ({
      ...invoice,
      dueDate: new Date(invoice.due_date),
      daysUntilDue: Math.ceil((new Date(invoice.due_date).getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    }))
    .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Due Date Customer</span>
            <Badge className="bg-blue-100 text-blue-800">
              {unpaidInvoices.length} Invoice Belum Lunas
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Nearest Due Date Card */}
          {nearestInvoice && (
            <Card className="border-orange-200 bg-orange-50">
              <CardHeader>
                <CardTitle className="text-lg text-orange-800">Invoice Terdekat</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-muted-foreground">Invoice:</span>
                    <p className="font-semibold">{nearestInvoice.invoice_no}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Customer:</span>
                    <p className="font-semibold">{nearestInvoice.customer.display_name}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Due Date:</span>
                    <p className="font-semibold">{formatDate(nearestInvoice.due_date)}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Amount:</span>
                    <p className="font-semibold text-lg">{formatCurrency(nearestInvoice.total)}</p>
                  </div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg border">
                  <div className="text-sm text-muted-foreground mb-2">Countdown</div>
                  <div className={`text-2xl font-bold ${countdown === 'OVERDUE' ? 'text-red-600' : 'text-orange-600'}`}>
                    {countdown}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* All Invoices Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Semua Invoice Belum Lunas</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedInvoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">
                        {invoice.invoice_no}
                      </TableCell>
                      <TableCell>
                        {invoice.customer.display_name}
                      </TableCell>
                      <TableCell>
                        {formatDate(invoice.due_date)}
                      </TableCell>
                      <TableCell className="font-semibold">
                        {formatCurrency(invoice.total)}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(invoice)}>
                          {getStatusText(invoice)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {sortedInvoices.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  Tidak ada invoice yang belum lunas
                </div>
              )}
            </CardContent>
          </Card>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Ringkasan</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Total Invoice:</span>
                <p className="text-2xl font-bold text-blue-600">{unpaidInvoices.length}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Total Amount:</span>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(unpaidInvoices.reduce((sum, inv) => sum + inv.total, 0))}
                </p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Overdue:</span>
                <p className="text-2xl font-bold text-red-600">
                  {sortedInvoices.filter(inv => inv.daysUntilDue < 0).length}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
