import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Send, RefreshCw, Users, Building2, Phone, Mail, MapPin, Plus, Search, Edit, Trash2 } from 'lucide-react';
import { useBigCapital } from '@/hooks/useBigCapital';
import { toast } from 'sonner';

interface VendorAIChatProps {
  onBack: () => void;
}

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  vendors?: any[];
}

interface Vendor {
  id: string;
  display_name: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  currency_code?: string;
  balance?: number;
  created_at?: string;
  updated_at?: string;
}

export const VendorAIChat: React.FC<VendorAIChatProps> = ({ onBack }) => {
  const [activeTab, setActiveTab] = useState('chat');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [showAddVendor, setShowAddVendor] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    setMessages([{
      id: '1',
      text: 'Halo! Saya AI Vendor. Saya dapat membantu Anda mengelola vendor dan tagihan dari BigCapital. Ketik "vendor" untuk melihat daftar vendor, atau gunakan tab Contact List untuk mengelola kontak vendor.',
      isUser: false,
      timestamp: new Date(),
    }]);
    
    // Load vendors on component mount
    fetchVendors();
  }, []);

  useEffect(() => {
    // Filter vendors based on search term
    const filtered = vendors.filter(vendor =>
      vendor.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.phone?.includes(searchTerm)
    );
    setFilteredVendors(filtered);
  }, [vendors, searchTerm]);

  const fetchVendors = async () => {
    setLoading(true);
    try {
      // Mock vendor data - replace with actual API call
      const mockVendors: Vendor[] = [
        {
          id: '1',
          display_name: 'PT. Supplier Utama',
          email: '<EMAIL>',
          phone: '+62-21-1234567',
          website: 'www.supplierutama.com',
          address: 'Jl. Sudirman No. 123',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: -15000000,
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          display_name: 'CV. Mitra Jaya',
          email: '<EMAIL>',
          phone: '+62-21-9876543',
          address: 'Jl. Gatot Subroto No. 456',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: -8500000,
          created_at: '2024-02-20T14:30:00Z'
        },
        {
          id: '3',
          display_name: 'Toko Elektronik Sejahtera',
          email: '<EMAIL>',
          phone: '+62-21-5555666',
          address: 'Jl. Mangga Besar No. 789',
          city: 'Jakarta',
          country: 'Indonesia',
          currency_code: 'IDR',
          balance: -2300000,
          created_at: '2024-03-10T09:15:00Z'
        }
      ];
      
      setVendors(mockVendors);
      toast.success('Data vendor berhasil dimuat');
    } catch (error) {
      console.error('Error fetching vendors:', error);
      toast.error('Gagal memuat data vendor');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sendingMessage) return;
    
    const userMessage = messageInput.trim().toLowerCase();
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageInput,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setMessageInput('');
    setSendingMessage(true);

    try {
      let aiResponse: ChatMessage;

      if (userMessage.includes('vendor') || userMessage.includes('supplier')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: `Berikut adalah daftar vendor Anda (${vendors.length} vendor):`,
          isUser: false,
          timestamp: new Date(),
          vendors: vendors,
        };
      } else if (userMessage.includes('tagihan') || userMessage.includes('bill')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Untuk melihat tagihan vendor, silakan gunakan tab Transaksi atau ketik "vendor" untuk melihat daftar vendor terlebih dahulu.',
          isUser: false,
          timestamp: new Date(),
        };
      } else if (userMessage.includes('help') || userMessage.includes('bantuan')) {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Saya dapat membantu Anda dengan:\n\n• Melihat daftar vendor (ketik "vendor")\n• Mengelola kontak vendor (gunakan tab Contact List)\n• Informasi tagihan vendor\n• Status pembayaran\n\nApa yang ingin Anda lakukan?',
          isUser: false,
          timestamp: new Date(),
        };
      } else {
        aiResponse = {
          id: (Date.now() + 1).toString(),
          text: 'Maaf, saya belum memahami permintaan Anda. Silakan ketik "help" untuk melihat apa yang bisa saya bantu, atau ketik "vendor" untuk melihat daftar vendor.',
          isUser: false,
          timestamp: new Date(),
        };
      }

      setTimeout(() => {
        setMessages(prev => [...prev, aiResponse]);
      }, 500);

    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Maaf, terjadi kesalahan saat memproses permintaan Anda.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
    }).format(Math.abs(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  const getBalanceColor = (balance: number) => {
    if (balance < 0) return 'text-red-600'; // Owe money to vendor
    if (balance > 0) return 'text-green-600'; // Vendor owes money
    return 'text-gray-600'; // No balance
  };

  const getBalanceText = (balance: number) => {
    if (balance < 0) return `Hutang: ${formatCurrency(balance)}`;
    if (balance > 0) return `Piutang: ${formatCurrency(balance)}`;
    return 'Lunas';
  };

  return (
    <div className="flex flex-col h-[calc(100vh-60px)]">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <Avatar className="w-10 h-10">
          <AvatarFallback className="bg-green-500">
            AV
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <h3 className="font-semibold">AI Vendor</h3>
          <p className="text-sm text-muted-foreground">Vendor Management Assistant</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchVendors}
          disabled={loading}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <Send className="w-4 h-4" />
            Chat AI
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Contact List
          </TabsTrigger>
        </TabsList>

        {/* Chat Tab */}
        <TabsContent value="chat" className="flex-1 flex flex-col mt-0">
          {/* Messages */}
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {messages.map((message) => (
              <div key={message.id} className="space-y-2">
                <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                    message.isUser ? 'bg-green-500 text-white' : 'bg-muted text-foreground'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                    <span className={`text-xs block mt-1 ${
                      message.isUser ? 'text-green-100' : 'text-muted-foreground'
                    }`}>
                      {message.timestamp.toLocaleTimeString('id-ID')}
                    </span>
                  </div>
                </div>

                {/* Vendor List */}
                {message.vendors && message.vendors.length > 0 && (
                  <div className="space-y-2 max-w-md">
                    {message.vendors.map((vendor) => (
                      <Card key={vendor.id} className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-sm">{vendor.display_name}</h4>
                            <p className="text-xs text-muted-foreground">{vendor.email}</p>
                            <p className="text-xs text-muted-foreground">{vendor.phone}</p>
                            <p className={`text-xs font-medium ${getBalanceColor(vendor.balance)}`}>
                              {getBalanceText(vendor.balance)}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {vendor.currency_code}
                          </Badge>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            ))}

            {sendingMessage && (
              <div className="flex justify-start">
                <div className="bg-muted px-3 py-2 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t bg-background">
            <div className="flex gap-2 items-center">
              <Input
                placeholder="Ketik pesan Anda..."
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                className="flex-1"
                disabled={sendingMessage}
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || sendingMessage}
                className="shrink-0"
              >
                <Send className="w-4 h-4 mr-2" />
                {sendingMessage ? 'Mengirim...' : 'Kirim'}
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Contact List Tab */}
        <TabsContent value="contacts" className="flex-1 flex flex-col mt-0">
          <div className="p-4 space-y-4 flex-1 overflow-y-auto">
            {/* Search and Add */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Cari vendor..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={() => setShowAddVendor(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah
              </Button>
            </div>

            {/* Vendor Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-blue-600">{vendors.length}</h4>
                  <p className="text-sm text-muted-foreground">Total Vendor</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-red-600">
                    {vendors.filter(v => v.balance < 0).length}
                  </h4>
                  <p className="text-sm text-muted-foreground">Vendor Hutang</p>
                </div>
              </Card>
              <Card className="p-4">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-green-600">
                    {formatCurrency(vendors.reduce((sum, v) => sum + Math.abs(v.balance || 0), 0))}
                  </h4>
                  <p className="text-sm text-muted-foreground">Total Saldo</p>
                </div>
              </Card>
            </div>

            {/* Vendor List */}
            <div className="space-y-3">
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p className="text-muted-foreground">Memuat data vendor...</p>
                </div>
              ) : filteredVendors.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Tidak ada vendor ditemukan</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? 'Coba kata kunci lain' : 'Belum ada vendor yang terdaftar'}
                  </p>
                  <Button onClick={() => setShowAddVendor(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Tambah Vendor Pertama
                  </Button>
                </div>
              ) : (
                filteredVendors.map((vendor) => (
                  <Card key={vendor.id} className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => setSelectedVendor(vendor)}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Avatar className="w-12 h-12">
                          <AvatarFallback className="bg-green-100 text-green-700">
                            {vendor.display_name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold truncate">{vendor.display_name}</h3>
                          <div className="space-y-1 text-sm text-muted-foreground">
                            {vendor.email && (
                              <div className="flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                <span className="truncate">{vendor.email}</span>
                              </div>
                            )}
                            {vendor.phone && (
                              <div className="flex items-center gap-1">
                                <Phone className="w-3 h-3" />
                                <span>{vendor.phone}</span>
                              </div>
                            )}
                            {vendor.address && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span className="truncate">{vendor.address}, {vendor.city}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getBalanceColor(vendor.balance || 0)}`}>
                          {getBalanceText(vendor.balance || 0)}
                        </p>
                        <Badge variant="outline" className="mt-1">
                          {vendor.currency_code}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
